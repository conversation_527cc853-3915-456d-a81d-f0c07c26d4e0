#!/usr/bin/env python3
"""
测试改进的深度分层策略
验证"远距离精细，近距离粗糙"的分层效果
"""

import torch
import math

def depth_to_bins_lid(depth_map, depth_min=0.0, depth_max=1.0, num_bins=6):
    """
    改进的深度分层函数，使用反向二次函数实现"远距离精细，近距离粗糙"
    """
    # 生成分层边界，使用反向二次函数
    split_points = []
    for i in range(1, num_bins + 1):
        t = i / (num_bins + 1)
        # 使用反向二次函数 f(t) = 1 - (1-t)^2
        boundary = 1.0 - (1.0 - t) ** 2
        split_points.append(boundary)
    
    # 构建完整的边界列表
    boundaries = [0.0] + split_points + [1.0]
    boundaries = torch.tensor(boundaries, device=depth_map.device, dtype=depth_map.dtype)
    
    # 处理边界情况
    depth_map_clipped = torch.clamp(depth_map, 0.0, 1.0)
    
    # 区间查找
    indices = torch.searchsorted(boundaries[1:-1], depth_map_clipped, right=False)
    indices = torch.clamp(indices, 0, num_bins)
    
    return indices.long()

def analyze_new_strategy(num_bins=6):
    """分析新策略的区间分布"""
    print("=" * 80)
    print("新的深度分层策略分析")
    print("=" * 80)
    
    print(f"num_bins = {num_bins} (生成 {num_bins+1} 个区间)")
    
    # 生成边界
    split_points = []
    for i in range(1, num_bins + 1):
        t = i / (num_bins + 1)
        boundary = 1.0 - (1.0 - t) ** 2
        split_points.append(boundary)
    
    boundaries = [0.0] + split_points + [1.0]
    
    # 计算区间大小
    print("\n区间分布详情:")
    print("区间索引 | 区间范围                | 区间大小  | 特征")
    print("-" * 65)
    
    total_size = 0
    for i in range(len(boundaries) - 1):
        size = boundaries[i+1] - boundaries[i]
        total_size += size
        
        # 判断区间特征
        if i == 0:
            feature = "近距离（最粗糙）"
        elif i == len(boundaries) - 2:
            feature = "远距离（最精细）"
        elif i < len(boundaries) // 2:
            feature = "中近距离"
        else:
            feature = "中远距离"
        
        print(f"区间{i:2d}   | [{boundaries[i]:.6f}, {boundaries[i+1]:.6f}] | {size:.6f} | {feature}")
    
    print(f"\n总大小: {total_size:.6f}")
    
    # 分析区间大小变化趋势
    sizes = [boundaries[i+1] - boundaries[i] for i in range(len(boundaries) - 1)]
    
    print(f"\n区间大小变化趋势:")
    print(f"最大区间（近距离）: {max(sizes):.6f}")
    print(f"最小区间（远距离）: {min(sizes):.6f}")
    print(f"精细化比例: {max(sizes)/min(sizes):.2f}:1")
    
    return boundaries, sizes

def compare_with_original():
    """与原始LID方法对比"""
    print(f"\n{'='*80}")
    print("与原始LID方法对比")
    print(f"{'='*80}")
    
    num_bins = 6
    
    # 原始LID方法的边界计算（简化版）
    print("原始LID方法特点:")
    print("- 使用开方运算: indices = -0.5 + 0.5 * sqrt(1 + 8 * (depth - depth_min) / bin_size)")
    print("- 区间大小递增: 近距离小区间，远距离大区间")
    print("- 数值复杂度高: 涉及开方运算")
    
    print(f"\n新方法特点:")
    print("- 使用反向二次函数: f(t) = 1 - (1-t)^2")
    print("- 区间大小递减: 近距离大区间，远距离小区间")
    print("- 数值稳定性好: 只涉及简单的二次运算")
    print("- 符合深度感知需求: 远距离精细，近距离粗糙")

def test_depth_mapping():
    """测试具体深度值的映射"""
    print(f"\n{'='*80}")
    print("深度值映射测试")
    print(f"{'='*80}")
    
    # 测试深度值
    test_depths = torch.tensor([0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95, 0.99, 1.0])
    indices = depth_to_bins_lid(test_depths, num_bins=6)
    
    print("深度值映射结果:")
    print("深度值  | 区间索引 | 说明")
    print("-" * 35)
    
    for depth, idx in zip(test_depths, indices):
        if depth <= 0.3:
            desc = "近距离"
        elif depth <= 0.7:
            desc = "中距离"
        elif depth <= 0.9:
            desc = "中远距离"
        else:
            desc = "远距离"
        
        print(f"{depth:.2f}    |    {idx.item()}     | {desc}")

def test_distribution_analysis():
    """分析深度值分布"""
    print(f"\n{'='*80}")
    print("深度值分布分析")
    print(f"{'='*80}")
    
    # 创建均匀分布的深度图
    uniform_depths = torch.linspace(0, 1, 1000)
    indices = depth_to_bins_lid(uniform_depths, num_bins=6)
    
    # 统计每个区间的像素数量
    unique, counts = torch.unique(indices, return_counts=True)
    
    print("均匀深度分布下的区间像素统计:")
    print("区间索引 | 像素数量 | 百分比  | 特征")
    print("-" * 45)
    
    total_pixels = len(uniform_depths)
    for idx, count in zip(unique, counts):
        percentage = count.item() / total_pixels * 100
        
        if idx == 0:
            feature = "近距离（粗糙）"
        elif idx == len(unique) - 1:
            feature = "远距离（精细）"
        else:
            feature = "中等距离"
        
        print(f"区间{idx.item():2d}   | {count.item():4d}     | {percentage:5.1f}% | {feature}")

def test_edge_cases():
    """测试边界情况"""
    print(f"\n{'='*80}")
    print("边界情况测试")
    print(f"{'='*80}")
    
    # 测试超出范围的值
    edge_values = torch.tensor([-0.5, -0.1, 0.0, 0.5, 1.0, 1.1, 1.5])
    indices = depth_to_bins_lid(edge_values, num_bins=6)
    
    print("边界值处理测试:")
    print("输入深度 | 输出索引 | 说明")
    print("-" * 35)
    
    for val, idx in zip(edge_values, indices):
        if val < 0:
            desc = "负值被裁剪到0"
        elif val > 1:
            desc = "超出1被裁剪"
        else:
            desc = "正常范围"
        
        print(f"{val:6.1f}   |    {idx.item()}     | {desc}")

def performance_comparison():
    """性能对比测试"""
    print(f"\n{'='*80}")
    print("性能对比")
    print(f"{'='*80}")
    
    import time
    
    # 创建大尺寸测试数据
    large_depth_map = torch.rand(480, 720)  # 大图像
    
    # 测试新方法的性能
    start_time = time.time()
    for _ in range(100):
        indices = depth_to_bins_lid(large_depth_map, num_bins=6)
    new_method_time = (time.time() - start_time) / 100 * 1000
    
    print(f"新方法性能测试:")
    print(f"图像大小: {large_depth_map.shape}")
    print(f"平均耗时: {new_method_time:.2f} ms")
    print(f"计算复杂度: O(num_bins) 边界生成 + O(N log num_bins) 查找")
    print(f"数值稳定性: 优秀（仅涉及二次运算）")

def main():
    """主测试函数"""
    print("开始测试改进的深度分层策略")
    
    # 运行所有分析
    analyze_new_strategy(num_bins=6)
    compare_with_original()
    test_depth_mapping()
    test_distribution_analysis()
    test_edge_cases()
    performance_comparison()
    
    print(f"\n{'='*80}")
    print("总结")
    print(f"{'='*80}")
    
    print("新的深度分层策略优势:")
    print("✓ 符合深度感知需求：远距离精细，近距离粗糙")
    print("✓ 数值稳定性好：避免了开方运算")
    print("✓ 计算效率高：简单的二次函数")
    print("✓ 物理意义明确：符合深度图像处理实际需求")
    print("✓ 接口兼容：保持原有函数签名")
    print("✓ 边界处理：正确处理超出范围的值")
    
    print(f"\n推荐使用场景:")
    print("- 深度图像处理和分析")
    print("- 需要远距离精细分辨率的应用")
    print("- 对数值稳定性有要求的系统")
    print("- 需要高效计算的实时应用")

if __name__ == "__main__":
    main()
