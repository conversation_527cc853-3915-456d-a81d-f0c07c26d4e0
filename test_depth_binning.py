#!/usr/bin/env python3
"""
测试新的指数深度分层方法
验证 depth_to_bins_lid 函数的正确性和性能
"""

import torch
import math
import matplotlib.pyplot as plt
import numpy as np
from model import depth_to_bins_lid

def test_exponential_binning():
    """测试指数分层的基本功能"""
    print("=" * 60)
    print("测试指数深度分层方法")
    print("=" * 60)
    
    # 测试不同的num_bins值
    test_cases = [
        {"num_bins": 4, "name": "4分层（5个区间）"},
        {"num_bins": 6, "name": "6分层（7个区间）"},
        {"num_bins": 8, "name": "8分层（9个区间）"},
    ]
    
    for case in test_cases:
        num_bins = case["num_bins"]
        name = case["name"]
        
        print(f"\n{name}:")
        print(f"num_bins = {num_bins}")
        
        # 生成边界值用于验证
        boundaries = [0.0]
        for i in range(num_bins + 1, 0, -1):
            boundaries.append(math.exp(-i))
        boundaries.append(1.0)
        
        print("区间边界:")
        for i in range(len(boundaries) - 1):
            print(f"  区间{i}: [{boundaries[i]:.6f}, {boundaries[i+1]:.6f}]")
        
        # 测试一些特定的深度值
        test_depths = torch.tensor([0.0, 0.001, 0.01, 0.05, 0.1, 0.2, 0.5, 0.8, 1.0])
        indices = depth_to_bins_lid(test_depths, num_bins=num_bins)
        
        print("测试深度值映射:")
        for depth, idx in zip(test_depths, indices):
            print(f"  深度 {depth:.3f} -> 区间 {idx.item()}")

def test_shape_compatibility():
    """测试不同输入形状的兼容性"""
    print(f"\n{'='*60}")
    print("测试形状兼容性")
    print(f"{'='*60}")
    
    # 测试不同形状的输入
    shapes = [
        (10, 15),           # 2D
        (2, 10, 15),        # 3D (batch)
        (4, 1, 10, 15),     # 4D (batch, channel)
    ]
    
    for shape in shapes:
        print(f"\n测试形状: {shape}")
        
        # 创建随机深度图
        depth_map = torch.rand(shape)
        
        # 应用深度分层
        indices = depth_to_bins_lid(depth_map, num_bins=6)
        
        print(f"  输入形状: {depth_map.shape}")
        print(f"  输出形状: {indices.shape}")
        print(f"  输出数据类型: {indices.dtype}")
        print(f"  索引范围: [{indices.min().item()}, {indices.max().item()}]")
        
        # 验证形状一致性
        assert depth_map.shape == indices.shape, "输入输出形状不一致"
        assert indices.dtype == torch.long, "输出类型应为long"
        assert indices.min() >= 0 and indices.max() <= 6, "索引超出有效范围"
        
        print("  ✓ 形状兼容性测试通过")

def test_boundary_cases():
    """测试边界情况"""
    print(f"\n{'='*60}")
    print("测试边界情况")
    print(f"{'='*60}")
    
    # 测试超出范围的值
    test_values = torch.tensor([-0.5, -0.1, 0.0, 0.5, 1.0, 1.1, 1.5])
    indices = depth_to_bins_lid(test_values, num_bins=6)
    
    print("边界值测试:")
    for val, idx in zip(test_values, indices):
        print(f"  深度 {val:.1f} -> 区间 {idx.item()}")
    
    # 验证裁剪是否正确
    assert all(0 <= idx <= 6 for idx in indices), "边界值处理不正确"
    print("  ✓ 边界情况测试通过")

def visualize_binning():
    """可视化深度分层效果"""
    print(f"\n{'='*60}")
    print("生成深度分层可视化")
    print(f"{'='*60}")
    
    # 创建深度值序列
    depth_values = torch.linspace(0, 1, 1000)
    
    # 测试不同的num_bins
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    axes = axes.flatten()
    
    num_bins_list = [4, 6, 8, 10]
    
    for i, num_bins in enumerate(num_bins_list):
        indices = depth_to_bins_lid(depth_values, num_bins=num_bins)
        
        axes[i].plot(depth_values.numpy(), indices.numpy(), 'b-', linewidth=2)
        axes[i].set_title(f'num_bins={num_bins} ({num_bins+1}个区间)')
        axes[i].set_xlabel('深度值')
        axes[i].set_ylabel('区间索引')
        axes[i].grid(True, alpha=0.3)
        axes[i].set_xlim(0, 1)
        axes[i].set_ylim(-0.5, num_bins + 0.5)
        
        # 添加区间边界线
        boundaries = [0.0]
        for j in range(num_bins + 1, 0, -1):
            boundaries.append(math.exp(-j))
        boundaries.append(1.0)
        
        for boundary in boundaries[1:-1]:
            axes[i].axvline(x=boundary, color='r', linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig('depth_binning_visualization.png', dpi=300, bbox_inches='tight')
    print("可视化图像已保存为 'depth_binning_visualization.png'")

def compare_with_original():
    """与原始LID方法进行对比（如果可用）"""
    print(f"\n{'='*60}")
    print("新方法优势分析")
    print(f"{'='*60}")
    
    # 创建测试深度图
    depth_map = torch.rand(100, 150)
    
    # 使用新方法
    new_indices = depth_to_bins_lid(depth_map, num_bins=6)
    
    print("新指数分层方法特点:")
    print("1. 固定区间：不依赖输入数据范围")
    print("2. 指数分布：近距离有更高分辨率")
    print("3. 数值稳定：避免开方运算")
    print("4. 语义明确：每个区间有物理意义")
    print("5. 灵活配置：支持不同num_bins值")
    
    # 分析分布
    unique, counts = torch.unique(new_indices, return_counts=True)
    print(f"\n区间分布统计（随机深度图）:")
    for idx, count in zip(unique, counts):
        percentage = count.item() / new_indices.numel() * 100
        print(f"  区间{idx.item()}: {count.item()} 像素 ({percentage:.1f}%)")

def performance_test():
    """性能测试"""
    print(f"\n{'='*60}")
    print("性能测试")
    print(f"{'='*60}")
    
    import time
    
    # 测试不同大小的输入
    sizes = [
        (60, 90),           # 小图像
        (240, 360),         # 中等图像
        (480, 720),         # 大图像
    ]
    
    for size in sizes:
        depth_map = torch.rand(size)
        
        # 测试执行时间
        start_time = time.time()
        for _ in range(100):  # 运行100次取平均
            indices = depth_to_bins_lid(depth_map, num_bins=6)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100 * 1000  # 转换为毫秒
        
        print(f"图像大小 {size}: 平均耗时 {avg_time:.2f} ms")

def main():
    """主测试函数"""
    print("开始测试新的指数深度分层方法")
    
    # 运行所有测试
    test_exponential_binning()
    test_shape_compatibility()
    test_boundary_cases()
    
    try:
        visualize_binning()
    except ImportError:
        print("matplotlib不可用，跳过可视化测试")
    
    compare_with_original()
    performance_test()
    
    print(f"\n{'='*60}")
    print("所有测试完成！")
    print(f"{'='*60}")
    
    print("\n总结:")
    print("✓ 指数深度分层方法实现正确")
    print("✓ 支持不同的num_bins配置")
    print("✓ 形状兼容性良好")
    print("✓ 边界情况处理正确")
    print("✓ 性能表现良好")

if __name__ == "__main__":
    main()
