#!/usr/bin/env python3
"""
测试 Heat2D 整合到 ViTsubmodules.py 的功能
验证 HeatMixTransformerEncoderLayer 的正确性和兼容性
"""

import torch
import torch.nn as nn
from ViTsubmodules import (
    MixTransformerEncoderLayer,
    HeatMixTransformerEncoderLayer,
    Heat2D_ViT
)

def test_heat2d_basic():
    """测试基础的Heat2D_ViT模块"""
    print("=" * 60)
    print("测试 Heat2D_ViT 基础功能")
    print("=" * 60)

    # 测试参数
    batch_size = 2
    seq_len = 15 * 23  # 深度图像的序列长度
    dim = 384

    # 创建Heat2D模块
    heat2d = Heat2D_ViT(
        dim=dim,
        resolution=(15, 23),  # 非正方形分辨率
        infer_mode=False
    )

    # 测试输入
    x = torch.randn(batch_size, seq_len, dim)
    print(f"输入形状: {x.shape}")

    # 前向传播
    with torch.no_grad():
        output = heat2d(x, H=15, W=23)

    print(f"输出形状: {output.shape}")
    print(f"形状保持: {x.shape == output.shape}")
    print("✓ Heat2D_ViT 基础测试通过\n")

    return True

def test_encoder_comparison():
    """对比测试原始编码器和Heat2D编码器"""
    print("=" * 60)
    print("对比测试：原始 vs Heat2D 编码器")
    print("=" * 60)

    # 测试参数
    batch_size = 2
    input_channels = 32
    height, width = 15, 23
    embed_dim = 128

    # 创建测试输入
    test_input = torch.randn(batch_size, input_channels, height, width)
    print(f"测试输入形状: {test_input.shape}")

    # 编码器参数
    encoder_params = {
        'in_channels': input_channels,
        'out_channels': embed_dim,
        'patch_size': 3,
        'stride': 2,
        'padding': 1,
        'n_layers': 2,
        'reduction_ratio': 4,
        'num_heads': 8,
        'expansion_factor': 4
    }

    # 测试原始编码器
    print("\n1. 原始 MixTransformerEncoderLayer:")
    try:
        original_encoder = MixTransformerEncoderLayer(**encoder_params)

        with torch.no_grad():
            original_output = original_encoder(test_input)

        print(f"   输出形状: {original_output.shape}")
        print("   ✓ 原始编码器测试成功")

    except Exception as e:
        print(f"   ✗ 原始编码器测试失败: {e}")
        return False

    # 测试Heat2D编码器
    print("\n2. HeatMixTransformerEncoderLayer (修正版本):")
    try:
        heat_params = encoder_params.copy()
        # 注意：这里的resolution参数现在不会影响实际计算，因为我们使用动态尺寸
        heat_params['resolution'] = (height, width)  # 这个参数现在主要用于配置记录

        heat_encoder = HeatMixTransformerEncoderLayer(**heat_params)

        with torch.no_grad():
            heat_output = heat_encoder(test_input)

        print(f"   输出形状: {heat_output.shape}")
        print(f"   配置信息: {heat_encoder.config}")
        print("   ✓ Heat2D编码器测试成功")

        # 验证尺寸逻辑
        print(f"   详细分析:")
        print(f"     原始输入: {test_input.shape}")
        print(f"     预期降采样后尺寸: H'≈{height//2}, W'≈{width//2}")
        print(f"     实际输出: {heat_output.shape}")

    except Exception as e:
        print(f"   ✗ Heat2D编码器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    # 形状兼容性检查
    print(f"\n3. 兼容性检查:")
    print(f"   原始输出: {original_output.shape}")
    print(f"   Heat2D输出: {heat_output.shape}")

    if original_output.shape == heat_output.shape:
        print("   ✓ 输出形状完全一致，接口兼容")
        print("   ✓ 尺寸不匹配问题已修复")
        return True
    else:
        print("   ✗ 输出形状不一致")
        return False

def test_direct_creation():
    """测试直接创建HeatMixTransformerEncoderLayer"""
    print("=" * 60)
    print("测试直接创建编码器")
    print("=" * 60)

    # 测试参数
    batch_size = 2
    input_channels = 32
    height, width = 15, 23

    test_input = torch.randn(batch_size, input_channels, height, width)
    print(f"测试输入形状: {test_input.shape}")

    try:
        # 直接创建HeatMixTransformerEncoderLayer
        encoder = HeatMixTransformerEncoderLayer(
            in_channels=input_channels,
            out_channels=256,
            patch_size=3,
            stride=2,
            padding=1,
            n_layers=2,
            reduction_ratio=4,  # 保留以维持接口兼容性
            num_heads=8,        # 保留以维持接口兼容性
            expansion_factor=4,
            resolution=(height, width),
            infer_mode=False
        )

        print(f"编码器配置: {encoder.config}")

        # 前向传播
        with torch.no_grad():
            output = encoder(test_input)

        print(f"输出形状: {output.shape}")
        print("✓ 直接创建测试成功")
        return True

    except Exception as e:
        print(f"✗ 直接创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始 Heat2D 整合测试")
    print("=" * 80)

    # 运行所有测试
    tests = [
        ("Heat2D基础功能", test_heat2d_basic),
        ("编码器对比", test_encoder_comparison),
        ("直接创建", test_direct_creation)
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))

    # 输出测试结果
    print("=" * 80)
    print("测试结果汇总")
    print("=" * 80)

    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")

    # 总结
    passed = sum(1 for _, result in results if result)
    total = len(results)

    print(f"\n总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 所有测试通过！Heat2D 整合成功！")
        print("\n使用建议:")
        print("1. 使用 HeatMixTransformerEncoderLayer 替代 MixTransformerEncoderLayer")
        print("2. 设置 resolution=(15, 23) 以适配深度图像")
        print("3. 可以使用 create_heat2d_depth_encoder() 工厂函数快速创建")
    else:
        print("❌ 部分测试失败，请检查实现")

    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
