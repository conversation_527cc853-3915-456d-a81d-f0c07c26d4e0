"""
深度图HeatViT使用示例
===================

本文件展示如何使用修改后的vheat_vit.py来处理深度图数据

输入格式: [B, H*W, C] = [B, 15*23, 32]
其中:
- B: 批次大小
- H: 高度 = 15
- W: 宽度 = 23  
- C: 通道数 = 32
"""

import torch
import torch.nn as nn
from vheat_vit import heat_vit_base_depth, heat_vit_tiny_depth, heat_vit_large_depth


def create_sample_depth_data(batch_size=4):
    """
    创建示例深度图数据
    
    Args:
        batch_size (int): 批次大小
        
    Returns:
        torch.Tensor: [B, H*W, C] 形状的深度图数据
    """
    H, W, C = 15, 23, 32
    depth_data = torch.randn(batch_size, H * W, C)
    
    print(f"创建深度图数据:")
    print(f"  - 空间尺寸: {H} x {W}")
    print(f"  - 通道数: {C}")
    print(f"  - 批次大小: {batch_size}")
    print(f"  - 总形状: {depth_data.shape}")
    
    return depth_data


def example_feature_extraction():
    """
    示例1: 特征提取模式
    """
    print("\n" + "="*50)
    print("示例1: 深度图特征提取")
    print("="*50)
    
    # 创建模型（特征提取模式）
    model = heat_vit_base_depth(
        input_shape=(15, 23),  # 空间形状
        in_chans=32,           # 输入通道数
        embed_dim=768,         # 嵌入维度
        num_classes=0          # 0表示特征提取模式
    )
    
    # 创建示例数据
    depth_data = create_sample_depth_data(batch_size=2)
    
    # 前向传播
    with torch.no_grad():
        features = model(depth_data)
    
    print(f"\n模型输出:")
    print(f"  - 输入形状: {depth_data.shape}")
    print(f"  - 输出形状: {features.shape}")
    print(f"  - 输出含义: [批次大小, 位置数量, 特征维度]")
    
    return features


def example_classification():
    """
    示例2: 分类模式
    """
    print("\n" + "="*50)
    print("示例2: 深度图分类")
    print("="*50)
    
    # 创建模型（分类模式）
    model = heat_vit_base_depth(
        input_shape=(15, 23),  # 空间形状
        in_chans=32,           # 输入通道数
        embed_dim=768,         # 嵌入维度
        num_classes=10         # 10类分类任务
    )
    
    # 创建示例数据
    depth_data = create_sample_depth_data(batch_size=3)
    
    # 前向传播
    with torch.no_grad():
        logits = model(depth_data)
        probabilities = torch.softmax(logits, dim=-1)
    
    print(f"\n模型输出:")
    print(f"  - 输入形状: {depth_data.shape}")
    print(f"  - Logits形状: {logits.shape}")
    print(f"  - 概率形状: {probabilities.shape}")
    print(f"  - 预测类别: {torch.argmax(probabilities, dim=-1)}")


def example_different_sizes():
    """
    示例3: 不同规模的模型
    """
    print("\n" + "="*50)
    print("示例3: 不同规模模型对比")
    print("="*50)
    
    # 创建示例数据
    depth_data = create_sample_depth_data(batch_size=1)
    
    # 不同规模的模型
    models = {
        "Tiny": heat_vit_tiny_depth,
        "Base": heat_vit_base_depth,
        "Large": heat_vit_large_depth
    }
    
    for name, model_fn in models.items():
        print(f"\n{name} 模型:")
        
        # 创建模型
        model = model_fn(
            input_shape=(15, 23),
            in_chans=32,
            num_classes=0  # 特征提取模式
        )
        
        # 计算参数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        # 前向传播
        with torch.no_grad():
            features = model(depth_data)
        
        print(f"  - 总参数量: {total_params:,}")
        print(f"  - 可训练参数: {trainable_params:,}")
        print(f"  - 输出形状: {features.shape}")


def example_custom_shape():
    """
    示例4: 自定义输入形状
    """
    print("\n" + "="*50)
    print("示例4: 自定义输入形状")
    print("="*50)
    
    # 自定义形状参数
    custom_H, custom_W, custom_C = 20, 30, 64
    custom_batch_size = 2
    
    print(f"自定义参数:")
    print(f"  - 空间尺寸: {custom_H} x {custom_W}")
    print(f"  - 通道数: {custom_C}")
    print(f"  - 批次大小: {custom_batch_size}")
    
    # 创建自定义模型
    model = heat_vit_base_depth(
        input_shape=(custom_H, custom_W),
        in_chans=custom_C,
        embed_dim=512,  # 自定义嵌入维度
        depth=8,        # 自定义层数
        num_classes=5   # 5类分类
    )
    
    # 创建自定义数据
    custom_data = torch.randn(custom_batch_size, custom_H * custom_W, custom_C)
    
    # 前向传播
    with torch.no_grad():
        output = model(custom_data)
    
    print(f"\n结果:")
    print(f"  - 输入形状: {custom_data.shape}")
    print(f"  - 输出形状: {output.shape}")


def example_training_setup():
    """
    示例5: 训练设置
    """
    print("\n" + "="*50)
    print("示例5: 训练设置示例")
    print("="*50)
    
    # 创建模型
    model = heat_vit_base_depth(
        input_shape=(15, 23),
        in_chans=32,
        embed_dim=768,
        num_classes=10,
        drop_path_rate=0.1  # 随机深度
    )
    
    # 设置优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.05)
    
    # 设置损失函数
    criterion = nn.CrossEntropyLoss()
    
    # 创建示例训练数据
    train_data = create_sample_depth_data(batch_size=4)
    train_labels = torch.randint(0, 10, (4,))  # 随机标签
    
    print(f"训练设置:")
    print(f"  - 优化器: AdamW")
    print(f"  - 学习率: 1e-4")
    print(f"  - 权重衰减: 0.05")
    print(f"  - 损失函数: CrossEntropyLoss")
    
    # 模拟一个训练步骤
    model.train()
    optimizer.zero_grad()
    
    # 前向传播
    outputs = model(train_data)
    loss = criterion(outputs, train_labels)
    
    # 反向传播
    loss.backward()
    optimizer.step()
    
    print(f"\n训练步骤:")
    print(f"  - 输入形状: {train_data.shape}")
    print(f"  - 输出形状: {outputs.shape}")
    print(f"  - 标签形状: {train_labels.shape}")
    print(f"  - 损失值: {loss.item():.4f}")


if __name__ == "__main__":
    print("深度图HeatViT使用示例")
    print("="*60)
    
    # 运行所有示例
    example_feature_extraction()
    example_classification()
    example_different_sizes()
    example_custom_shape()
    example_training_setup()
    
    print("\n" + "="*60)
    print("所有示例运行完成！")
    print("="*60)
