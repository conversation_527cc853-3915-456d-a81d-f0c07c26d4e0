"""
@authors: <PERSON>, et. al
@organization: GRASP Lab, University of Pennsylvania
@date: ...
@license: ...

@brief: This module contains the submodules for ViT that were used in the paper "Utilizing vision transformer models for end-to-end vision-based
quadrotor obstacle avoidance" by <PERSON><PERSON><PERSON><PERSON>, et. al

@source: https://github.com/git-dhruv/Segformer
"""

import torch
import numpy as np
import torch.nn as nn
from einops import rearrange
import torch.nn.functional as F

def get_relative_distances(window_height, window_width):
    """
    生成窗口内像素的相对位置坐标差矩阵
    参数：
        window_size: 窗口大小（正方形窗口边长）
    返回：
        distances: 相对位置坐标差矩阵 [window_size^2, window_size^2, 2]
                  每个元素[i,j]表示第i个位置与第j个位置的(x,y)坐标差
    """
    # 生成网格坐标：创建window_size x window_size网格中每个像素的坐标
    indices = torch.tensor(np.array(
        [[x, y] for x in range(window_height) for y in range(window_width)]))
    # 计算所有位置对的坐标差：通过广播机制实现矩阵减法
    distances = indices[None, :, :] - indices[:, None, :]
    return distances

class RadixSoftmax(nn.Module):
    """
    RadixSoftmax模块：实现多头注意力中的径向softmax操作
    参数：
        radix: 径向数量
        cardinality: 卡尔基数
    """
    def __init__(self, radix, cardinality):
        super(RadixSoftmax, self).__init__()
        self.radix = radix
        self.cardinality = cardinality

    def forward(self, x):
        """
        前向传播函数
        参数：
            x: 输入张量
        返回：
            输出张量
        """
        x = torch.sigmoid(x)  # 使用sigmoid激活函数
        return x

class SplitAttn(nn.Module):
    """
    Split Attention模块：实现多头注意力中的分裂注意力机制
    参数：
        input_dim: 输入维度
    """
    def __init__(self, input_dim):
        super(SplitAttn, self).__init__()
        self.input_dim = input_dim

        self.fc1 = nn.Linear(input_dim, input_dim, bias=False)
        self.bn1 = nn.LayerNorm(input_dim)
        self.act1 = nn.ReLU()
        self.fc2 = nn.Linear(input_dim, input_dim * 2, bias=False)  # 从3改为2

        self.rsoftmax = RadixSoftmax(2, 1)  # 从3改为2

    def forward(self, window_list):
        """
        前向传播函数
        参数：
            window_list: 包含多个窗口特征的列表，每个窗口特征形状为(B, H, W, C)
        返回：
            输出张量
        """
        # window list: [(B, H, W, C) * 2]
        assert len(window_list) == 2, 'only 2 windows are supported'  # 从3改为2

        sw, mw = window_list[0], window_list[1]  # 只使用两个窗口
        B, H = sw.shape[0], sw.shape[1]

        # global average pooling, B, L, H, W, C
        x_gap = sw + mw  # 只有两个窗口相加
        # B, 1, 1, C
        x_gap = x_gap.mean((1, 2), keepdim=True)
        x_gap = self.act1(self.bn1(self.fc1(x_gap)))
        # B, 1, 1, 2C
        x_attn = self.fc2(x_gap)
        # B 1 1 2C
        x_attn = self.rsoftmax(x_attn).view(B, 1, 1, -1)

        out = sw * x_attn[:, :, :, 0:self.input_dim] + \
              mw * x_attn[:, :, :, self.input_dim:2*self.input_dim]

        return out

class OverlapPatchMerging(nn.Module):
    """
    重叠补丁合并层
    功能：
        将输入图像分割成重叠的补丁，并通过卷积操作降低维度
    参数：
        in_channels: 输入通道数
        out_channels: 输出通道数(嵌入维度)
        patch_size: 补丁大小(卷积核大小)
        stride: 步长，控制补丁之间的重叠程度
        padding: 填充大小
    """
    def __init__(self, in_channels, out_channels, patch_size, stride, padding):
        super().__init__()
        # 卷积层：用于提取补丁并降低维度
        self.cn1 = nn.Conv2d(in_channels, out_channels, kernel_size=patch_size, stride=stride, padding=padding)
        # 层归一化：对每个位置的特征进行归一化
        self.layerNorm = nn.LayerNorm(out_channels)

    def forward(self, patches):
        """
        前向传播函数
        参数：
            patches: 形状为(B, C, H, W)的张量，其中
                B是批次大小
                C是通道数
                H和W是高度和宽度
        返回：
            形状为(B, N, EmbedDim)的张量，其中
                N是补丁数量(H * W)
                EmbedDim是嵌入维度(out_channels)
        """
        x = self.cn1(patches)  # 输入形状(B, C, H, W)，输出形状(B, out_channels, H', W')
        _, _, H, W = x.shape
        # 将特征图展平为序列形式，形状变为(B, H' * W', out_channels)
        x = x.flatten(2).transpose(1, 2)
        # 应用层归一化
        x = self.layerNorm(x)
        return x, H, W  # 返回(B, N, EmbedDim)和特征图尺寸

class OverlapPatchMerging2(nn.Module):
    """
    重叠补丁合并层

    这个层将输入图像分割成重叠的补丁(patch)，并通过卷积操作降低维度
    与标准ViT不同，这里使用重叠的补丁而不是不重叠的补丁，可以保留更多的空间信息
    """
    def __init__(self, in_channels, out_channels, patch_size, stride, padding):
        """
        初始化重叠补丁合并层

        参数:
            in_channels: 输入通道数
            out_channels: 输出通道数(嵌入维度)
            patch_size: 补丁大小(卷积核大小)
            stride: 步长，控制补丁之间的重叠程度
            padding: 填充大小
        """
        super().__init__()
        # 使用卷积层来实现补丁提取和特征映射
        self.cn1 = nn.Conv2d(in_channels, out_channels, kernel_size=patch_size, stride=stride, padding=padding)
        # 层归一化，应用于嵌入维度
        self.layerNorm = nn.LayerNorm(out_channels)

    def forward(self, patches):
        """
        前向传播函数

        参数:
            patches: 形状为(B, C, H, W)的张量，其中
                B是批次大小
                C是通道数
                H和W是高度和宽度

        返回:
            x: 形状为(B, N, E)的张量，其中
                N是补丁数量(H*W)
                E是嵌入维度(out_channels)
            H: 输出特征图的高度
            W: 输出特征图的宽度
        """
        # 通过卷积提取补丁特征
        x = self.cn1(patches)
        # 获取输出特征图的形状
        _, _, H, W = x.shape
        # 将特征图重塑为序列形式
        # 从(B,C,H,W)变为(B,H*W,C)，即(批次,序列长度,嵌入维度)
        x = rearrange(x, 'b c h w -> b h w c')
        # x = x.transpose(1, 2)  # 先展平为(B,C,H*W)，再转置为(B,H*W,C)
        # 应用层归一化
        x = self.layerNorm(x)
        # 返回序列表示和特征图尺寸
        return x  # 返回(B,N,E)和特征图尺寸

class SEModule(nn.Module):
    def __init__(self, in_channels, reduction_ratio=2):
        super(SEModule, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction_ratio, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction_ratio, in_channels, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)
    
class EfficientSelfAttention(nn.Module):
    def __init__(self, channels, reduction_ratio, num_heads):
        super().__init__()
        assert channels % num_heads == 0, f"channels {channels} should be divided by num_heads {num_heads}."

        self.heads= num_heads

        #### Self Attention Block consists of 2 parts - Reduction and then normal Attention equation of queries and keys###

        # Reduction Parameters #
        self.cn1 = nn.Conv2d(in_channels=channels, out_channels=channels, kernel_size=reduction_ratio, stride= reduction_ratio)
        self.ln1 = nn.LayerNorm(channels)
        # Attention Parameters #
        self.keyValueExtractor = nn.Linear(channels, channels * 2)
        self.query = nn.Linear(channels, channels)
        self.smax = nn.Softmax(dim=-1)
        self.finalLayer = nn.Linear(channels, channels)


    def forward(self, x, H, W):

        """ Perform self attention with reduced sequence length

        :param x: tensor of shape (B, N, C) where
            B is the batch size,
            N is the number of queries (equal to H * W)
            C is the number of channels
        :return: tensor of shape (B, N, C)
        """
        B,N,C = x.shape
        # B, N, C -> B, C, N
        x1 = x.clone().permute(0,2,1)
        # BCN -> BCHW
        x1 = x1.reshape(B,C,H,W)
        x1 = self.cn1(x1)
        x1 = x1.reshape(B,C,-1).permute(0,2,1).contiguous()
        x1 = self.ln1(x1)
        # We have got the Reduced Embeddings! We need to extract key and value pairs now
        keyVal = self.keyValueExtractor(x1)
        keyVal = keyVal.reshape(B, -1 , 2, self.heads, int(C/self.heads)).permute(2,0,3,1,4).contiguous()
        k,v = keyVal[0],keyVal[1] #b,heads, n, c/heads
        q = self.query(x).reshape(B, N, self.heads, int(C/self.heads)).permute(0, 2, 1, 3).contiguous()

        dimHead = (C/self.heads)**0.5
        attention = self.smax(<EMAIL>(-2, -1)/dimHead)
        attention = (attention@v).transpose(1,2).reshape(B,N,C)

        x = self.finalLayer(attention) #B,N,C
        return x 

class SpatialWindowAttention(nn.Module):
    """
    空间窗口注意力模块，用于在特定窗口内计算自注意力机制。
    该模块通过降维操作减少计算量，同时引入相对位置编码增强注意力计算。
    """
    def __init__(self, channels, reduction_ratio, num_heads, window_size):
        """
        初始化空间窗口注意力模块。

        参数:
            channels: 输入特征的通道数。
            reduction_ratio: 降维比例，用于空间降采样。
            num_heads: 注意力头的数量。
            window_size: 窗口大小，用于划分特征图。
        """
        super().__init__()

        assert channels % num_heads == 0, f"channels {channels} should be divided by num_heads {num_heads}."

        self.heads = num_heads
        self.window_size = window_size

        #### 自注意力块由两部分组成 - 降维和标准的注意力计算 ###

        # 降维参数 #
        # 使用卷积进行空间降采样，减少序列长度
        self.cn1 = nn.Conv2d(in_channels=channels, out_channels=channels,
                            kernel_size=reduction_ratio, padding=(reduction_ratio-1)//2)
        self.ln1 = nn.LayerNorm(channels)

        self.relative_indices = get_relative_distances(window_size, window_size) + \
                                    window_size - 1
        # 可学习的位置偏置参数矩阵（形状根据相对位置范围确定）
        self.pos_embedding = nn.Parameter(torch.randn(2 * window_size - 1,
                                                          2 * window_size - 1))

        # 注意力参数 #
        # 从降采样的特征中提取键和值
        self.keyValueExtractor = nn.Linear(channels, channels * 2)  # 输出channels*2是因为同时生成key和value
        # 从原始特征中提取查询
        self.query = nn.Linear(channels, channels)

        # softmax用于注意力权重归一化
        self.smax = nn.Softmax(dim=-1)
        # 最终投影层
        self.finalLayer = nn.Linear(channels, channels)

    def forward(self, x):
        """
        前向传播函数。

        参数:
            x: 输入特征张量，形状为 (B, H, W, C)，其中
                B 是批次大小，
                H 是特征图高度，
                W 是特征图宽度，
                C 是通道数。

        返回:
            输出特征张量，形状为 (B, H, W, C)。
        """
        B, H, W, C = x.shape
        m = self.heads
        new_h = H // self.window_size
        new_w = W // self.window_size

        # 假设输入 x 形状为 (B, 60, 90, C)
        # 创建x的副本并重排维度，准备进行空间降采样
        # 从(B,H,W,C)变为(B,C,H,W)
        x1 = x.clone().permute(0, 3, 1, 2)  # 形状: (B, C, 60, 90)

        # 应用卷积进行降采样
        x1 = self.cn1(x1)  # 形状: (B, C, H', W')，H' 和 W' 由卷积参数决定
        # 假设 reduction_ratio 为 2，padding 为 0，H' = (60 - 2) // 2 + 1 = 30，W' = (90 - 2) // 2 + 1 = 45
        # 即形状变为 (B, C, 30, 45)

        # 重塑回序列形式(B,C,H',W') -> (B,H',W',C)
        x1 = x1.permute(0, 2, 3, 1).contiguous()  # 形状: (B, 30, 45, C)

        # 应用层归一化
        x1 = self.ln1(x1)  # 形状: (B, 30, 45, C)

        # 从降采样的特征中提取键和值
        kv = self.keyValueExtractor(x1).chunk(2, dim=-1)  # chunk返回元组，包含 key 和 value，每个形状为 (B, 30, 45, C)

        # 复杂的重塑操作，将key和value分离，并为多头注意力做准备
        # 最终形状: (B, m, new_h * new_w, window_size * window_size, C/m)
        k, v = map(
            lambda t: rearrange(t, 'b (new_h w_h) (new_w w_w) (m c) -> b m (new_h new_w) (w_h w_w) c',
                                m=m, w_h=self.window_size,
                                w_w=self.window_size,
                                new_h=new_h, new_w=new_w), kv)
        # 假设 window_size 为 10，new_h = 60 // 10 = 6，new_w = 90 // 10 = 9
        # k 和 v 形状: (B, m, 6 * 9, 10 * 10, C/m) = (B, m, 54, 100, C/m)

        # 从原始特征中提取查询
        q = self.query(x).contiguous()
        q = rearrange(q, 'b (new_h w_h) (new_w w_w) (m c) -> b m (new_h new_w) (w_h w_w) c',
                      m=m, w_h=self.window_size, w_w=self.window_size)
        # q 形状: (B, m, 54, 100, C/m)

        # 计算注意力权重的缩放因子
        dimHead = (C / self.heads) ** 0.5

        dots = q @ k.transpose(-2, -1)  # 形状: (B, m, 54, 100, 100)
        dots += self.pos_embedding[self.relative_indices[:, :, 0],
                                   self.relative_indices[:, :, 1]]

        attention = self.smax(dots / dimHead)  # 形状: (B, m, 54, 100, 100)

        attention = attention @ v  # 形状: (B, m, 54, 100, C/m)
        attention = rearrange(attention, 'b m (new_h new_w) (w_h w_w) c -> b (new_h w_h) (new_w w_w) (m c)', m=m,
                              w_h=self.window_size, w_w=self.window_size, new_h=new_h, new_w=new_w)
        # 形状: (B, 60, 90, C)

        # 通过最终投影层
        x = self.finalLayer(attention)  # 形状: (B, 60, 90, C)
        return x




class MixFFN(nn.Module):
    def __init__(self, channels, expansion_factor):
        super().__init__()
        expanded_channels = channels*expansion_factor
        #MLP Layer
        self.mlp1 = nn.Linear(channels, expanded_channels)
        #Depth Wise CNN Layer
        self.depthwise = nn.Conv2d(expanded_channels, expanded_channels, kernel_size=3,  padding='same', groups=channels)
        #GELU
        self.gelu = nn.GELU()
        #MLP to predict
        self.mlp2 = nn.Linear(expanded_channels, channels)

    def forward(self, x, H, W):
        """ Perform self attention with reduced sequence length

        :param x: tensor with shape (B, C, H, W) where
            B is the Batch size
            C is the number of Channels
            H and W are the Height and Width
        :return: tensor with shape (B, C, H, W)
        """
        # Input BNC instead of BCHW
        # BNC -> B,N,C*exp
        x = self.mlp1(x)
        B,N,C = x.shape
        # Prepare for the CNN operation, channel should be 1st dim
        # B,N, C*exp -> B, C*exp, H, W
        x = x.transpose(1,2).view(B,C,H,W)

        #Depth Conv - B, N, Cexp
        x = self.gelu(self.depthwise(x).flatten(2).transpose(1,2))

        #Back to the orignal shape
        x = self.mlp2(x) # BNC
        return x

class MixFFN2(nn.Module):
    """
    混合前馈网络

    这是一种结合了MLP和深度卷积的前馈网络，可以同时捕获通道间和空间信息
    相比标准Transformer中的纯MLP前馈网络，这种混合结构更适合处理图像数据
    """
    def __init__(self, channels, expansion_factor):
        """
        初始化混合前馈网络

        参数:
            channels: 输入通道数(嵌入维度)
            expansion_factor: 扩展因子，控制中间层的通道数扩展比例
        """
        super().__init__()
        # 计算扩展后的通道数
        expanded_channels = channels * expansion_factor

        # MLP层 - 将通道数扩展
        self.mlp1 = nn.Linear(channels, expanded_channels)

        # 深度卷积层 - 在扩展的通道上进行空间处理
        # groups=channels确保这是深度可分离卷积，每个通道独立卷积
        self.depthwise = nn.Conv2d(expanded_channels, expanded_channels,
                                  kernel_size=3, padding='same', groups=expanded_channels)

        # GELU激活函数 - 比ReLU更平滑的非线性激活
        self.gelu = nn.GELU()

        # 第二个MLP层 - 将通道数恢复到原始维度
        self.mlp2 = nn.Linear(expanded_channels, channels)

    def forward(self, x):
        """
        前向传播函数

        参数:
            x: 形状为(B, N, C)的张量，其中
                B是批次大小
                N是序列长度(补丁数量)
                C是通道数(嵌入维度)
            H: 特征图高度
            W: 特征图宽度

        返回:
            形状为(B, N, C)的张量，与输入形状相同
        """
        # 输入是(B,N,C)形式，而不是(B,C,H,W)

        # 通过第一个MLP层扩展通道维度
        # (B,N,C) -> (B,N,C*expansion_factor)
        x = self.mlp1(x)
        B, H, W, C = x.shape

        # 为卷积操作准备数据，需要将通道维度放在第二位
        # (B,N,C*exp) -> (B,C*exp,N) -> (B,C*exp,H,W)
        # x = x.transpose(1, 2).view(B, C, H, W)
        x = x.permute(0, 3, 1, 2)
        # 应用深度卷积，然后展平并转置回序列形式
        # (B,C*exp,H,W) -> (B,C*exp,H,W) -> (B,C*exp,N) -> (B,N,C*exp)
        # x = self.gelu(self.depthwise(x).flatten(2).transpose(1, 2))
        x = self.gelu(self.depthwise(x)).permute(0, 2, 3, 1)
        # 通过第二个MLP层恢复原始通道数
        # (B,N,C*exp) -> (B,N,C)
        x = self.mlp2(x)
        return x


class MixTransformerEncoderLayer(nn.Module):
    def __init__(self, in_channels, out_channels, patch_size, stride, padding,
                 n_layers, reduction_ratio, num_heads, expansion_factor):
        super().__init__()
        # 补丁合并层：将输入图像转换为序列形式
        self.patchMerge = OverlapPatchMerging(in_channels, out_channels, patch_size, stride, padding)
        # 自注意力层列表：每个层包含一个自注意力模块
        # self._attn = nn.ModuleList([EfficientSelfAttention(out_channels, reduction_ratio, num_heads) for _ in range(n_layers)])
        self._attn = nn.ModuleList([EfficientSelfAttention(out_channels, reduction_ratio, num_heads) for _ in range(n_layers)])
        # 前馈网络层列表：每个层包含一个前馈网络模块
        self._ffn = nn.ModuleList([MixFFN(out_channels, expansion_factor) for _ in range(n_layers)])
        # 层归一化层列表：每个层包含一个层归一化模块
        self._lNorm = nn.ModuleList([nn.LayerNorm(out_channels) for _ in range(n_layers)])

    def forward(self, x):
        """
        运行一个混合视觉Transformer块。

        :param x: 形状为(B, C, H, W)的张量，其中
            B是批次大小
            C是通道数
            H和W是高度和宽度
        :return: 形状为(B, C, H, W)的张量
        """
        B, C, H, W = x.shape  # 输入形状为(B, C, H, W)
        x, H, W = self.patchMerge(x)  # 补丁合并后，形状变为(B, N, EmbedDim)
        for i in range(len(self._attn)):
            # 自注意力层后，形状保持不变(B, N, EmbedDim)
            x = x + self._attn[i].forward(x, H, W)
            # 前馈网络层后，形状保持不变(B, N, EmbedDim)
            x = x + self._ffn[i].forward(x, H, W)
            # 层归一化后，形状保持不变(B, N, EmbedDim)
            x = self._lNorm[i].forward(x)
        # 将序列形式转换回空间形式，形状变为(B, C, H, W)
        x = x.reshape(B, H, W, -1).permute(0, 3, 1, 2).contiguous()
        return x

class PyramidWindowAttention(nn.Module):
    def __init__(self, channels, reduction_ratio, num_heads, window_size,
                  fuse_method='split_attn'):
        super().__init__()

        self.pwmsa = nn.ModuleList([])
        self.window_sizes = window_size

        for (num_head, ws) in zip(num_heads, window_size):
            self.pwmsa.append(SpatialWindowAttention(channels,
                                                     reduction_ratio,
                                                     num_head,
                                                     ws))
        self.fuse_method = fuse_method
        if fuse_method == 'split_attn':
            self.split_attn = SplitAttn(channels)

        # 存储融合权重
        self.fusion_weights = None

    def forward(self, x):
        output = None
        # naive fusion will just sum up all window attention output and do a
        # mean
        if self.fuse_method == 'naive':
            for wmsa in self.pwmsa:
                output = wmsa(x) if output is None else output + wmsa(x)
            return output / len(self.pwmsa)

        elif self.fuse_method == 'split_attn':
            window_list = [wmsa(x) for wmsa in self.pwmsa]
            return self.split_attn(window_list)





class MixWindowsTransformerEncoderLayer(nn.Module):
    """
    混合Transformer编码器层

    这是整个Vision Transformer的主要构建块，组合了补丁合并、自注意力和前馈网络
    可以堆叠多个这样的层来构建完整的Vision Transformer网络
    """
    def __init__(self, in_channels, out_channels, patch_size, stride, padding,
                 n_layers, reduction_ratio, num_heads, expansion_factor, window_size):
        """
        初始化混合Transformer编码器层

        参数:
            in_channels: 输入通道数
            out_channels: 输出通道数(嵌入维度)
            patch_size: 补丁大小
            stride: 步长
            padding: 填充大小
            n_layers: 内部Transformer块的数量
            reduction_ratio: 自注意力中的降采样比例
            num_heads: 注意力头的数量列表，每个窗口大小对应一个头数
            expansion_factor: 前馈网络中的扩展因子
            window_size: 窗口大小列表，用于多尺度窗口注意力
        """
        super().__init__()
        # 补丁合并层 - 将输入图像转换为序列表示
        self.patchMerge = OverlapPatchMerging2(in_channels, out_channels, patch_size, stride, padding)

        # 创建n_layers个自注意力层、前馈网络和层归一化
        # 注: 这里使用ModuleList而不是更简洁的实现，是因为每个前向函数的输入不同

        # 自注意力层列表
        self._attn = nn.ModuleList([
            PyramidWindowAttention(out_channels, reduction_ratio, num_heads, window_size)
            for _ in range(n_layers)
        ])

        # 前馈网络列表
        self._ffn = nn.ModuleList([
            MixFFN2(out_channels, expansion_factor)
            for _ in range(n_layers)
        ])

        # 层归一化列表
        self._lNorm = nn.ModuleList([
            nn.LayerNorm(out_channels)
            for _ in range(n_layers)
        ])

        # 保存窗口大小和层数，用于可视化
        self.window_size = window_size
        self.n_layers = n_layers

    def forward(self, x):
        """
        前向传播函数

        参数:
            x: 形状为(B, C, H, W)的张量，其中
                B是批次大小
                C是通道数
                H和W是高度和宽度

        返回:
            形状为(B, out_channels, H', W')的张量，其中H'和W'是降采样后的尺寸
        """
        # 通过补丁合并层，将图像转换为序列表示
        # (B,C,H,W) -> (B,H,W,C)，其中H,W是降采样后的高度和宽度
        x = self.patchMerge(x)

        # 依次通过每个Transformer块
        for i in range(len(self._attn)):
            # 残差连接 + 自注意力
            x = x + self._attn[i].forward(x)

            # 残差连接 + 前馈网络
            x = x + self._ffn[i].forward(x)

            # 层归一化
            x = self._lNorm[i].forward(x)

        # 将序列表示转换回空间表示
        x = x.permute(0, 3, 1, 2).contiguous()

        return x
