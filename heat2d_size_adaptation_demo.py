#!/usr/bin/env python3
"""
Heat2D_ViT 尺寸适配演示
展示如何处理不同矩形输入尺寸
"""

import torch
import torch.nn as nn
from ViTsubmodules import HeatMixTransformerEncoderLayer, Heat2D_ViT

def test_different_resolutions():
    """测试不同分辨率的适配能力"""
    print("=" * 80)
    print("Heat2D_ViT 不同分辨率适配测试")
    print("=" * 80)
    
    # 定义不同的测试尺寸
    test_cases = [
        {"name": "深度图像", "size": (15, 23), "channels": 32},
        {"name": "小尺寸矩形", "size": (12, 18), "channels": 32},
        {"name": "大尺寸矩形", "size": (20, 30), "channels": 32},
        {"name": "极端矩形", "size": (8, 40), "channels": 32},
        {"name": "正方形", "size": (16, 16), "channels": 32},
        {"name": "高瘦矩形", "size": (30, 10), "channels": 32},
    ]
    
    batch_size = 2
    embed_dim = 128
    
    for case in test_cases:
        print(f"\n{'='*50}")
        print(f"测试 {case['name']}: {case['size']}")
        print(f"{'='*50}")
        
        height, width = case['size']
        channels = case['channels']
        
        try:
            # 创建测试输入
            test_input = torch.randn(batch_size, channels, height, width)
            print(f"输入形状: {test_input.shape}")
            
            # 创建适配该尺寸的编码器
            encoder = HeatMixTransformerEncoderLayer(
                in_channels=channels,
                out_channels=embed_dim,
                patch_size=3,
                stride=2,
                padding=1,
                n_layers=2,
                reduction_ratio=4,
                num_heads=8,
                expansion_factor=4,
                resolution=(height, width),  # 关键：设置对应的分辨率
                infer_mode=False
            )
            
            # 前向传播
            with torch.no_grad():
                output = encoder(test_input)
            
            print(f"输出形状: {output.shape}")
            print(f"分辨率配置: {encoder.config['resolution']}")
            print(f"✓ {case['name']} 测试成功")
            
            # 计算降采样比例
            h_ratio = output.shape[2] / height
            w_ratio = output.shape[3] / width
            print(f"降采样比例: H={h_ratio:.2f}, W={w_ratio:.2f}")
            
        except Exception as e:
            print(f"✗ {case['name']} 测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_heat2d_direct():
    """直接测试 Heat2D_ViT 模块的尺寸适配"""
    print(f"\n{'='*80}")
    print("Heat2D_ViT 直接测试")
    print(f"{'='*80}")
    
    # 测试不同尺寸
    resolutions = [(15, 23), (20, 30), (12, 18), (16, 16)]
    dim = 256
    batch_size = 2
    
    for resolution in resolutions:
        print(f"\n测试分辨率: {resolution}")
        height, width = resolution
        seq_len = height * width
        
        # 创建 Heat2D_ViT 模块
        heat2d = Heat2D_ViT(
            dim=dim,
            resolution=resolution,  # 设置对应分辨率
            infer_mode=False
        )
        
        # 创建测试输入 [B, H*W, C]
        test_input = torch.randn(batch_size, seq_len, dim)
        print(f"  输入形状: {test_input.shape}")
        
        # 前向传播
        with torch.no_grad():
            output = heat2d(test_input, H=height, W=width)
        
        print(f"  输出形状: {output.shape}")
        print(f"  形状保持: {test_input.shape == output.shape}")
        print(f"  ✓ 分辨率 {resolution} 测试成功")

def create_adaptive_encoder(input_shape, channels=32, embed_dim=128):
    """
    创建自适应编码器的便捷函数
    
    Args:
        input_shape: (H, W) 输入图像的空间尺寸
        channels: 输入通道数
        embed_dim: 嵌入维度
    
    Returns:
        配置好的 HeatMixTransformerEncoderLayer
    """
    height, width = input_shape
    
    encoder = HeatMixTransformerEncoderLayer(
        in_channels=channels,
        out_channels=embed_dim,
        patch_size=3,
        stride=2,
        padding=1,
        n_layers=2,
        reduction_ratio=4,
        num_heads=8,
        expansion_factor=4,
        resolution=(height, width),  # 自动适配输入尺寸
        infer_mode=False
    )
    
    print(f"创建编码器 - 输入尺寸: {input_shape}, 输出维度: {embed_dim}")
    print(f"配置信息: {encoder.config}")
    
    return encoder

def practical_usage_example():
    """实际使用示例"""
    print(f"\n{'='*80}")
    print("实际使用示例")
    print(f"{'='*80}")
    
    # 场景1: 处理 [B, 32, 20, 30] 格式的输入
    print("\n场景1: 处理 [B, 32, 20, 30] 格式的输入")
    input_data = torch.randn(4, 32, 20, 30)  # 批次=4, 通道=32, 高=20, 宽=30
    
    # 创建适配该尺寸的编码器
    encoder1 = create_adaptive_encoder(
        input_shape=(20, 30),
        channels=32,
        embed_dim=256
    )
    
    with torch.no_grad():
        output1 = encoder1(input_data)
    
    print(f"输入: {input_data.shape} -> 输出: {output1.shape}")
    
    # 场景2: 处理 [B, 64, 12, 18] 格式的输入
    print("\n场景2: 处理 [B, 64, 12, 18] 格式的输入")
    input_data2 = torch.randn(2, 64, 12, 18)  # 批次=2, 通道=64, 高=12, 宽=18
    
    encoder2 = create_adaptive_encoder(
        input_shape=(12, 18),
        channels=64,
        embed_dim=512
    )
    
    with torch.no_grad():
        output2 = encoder2(input_data2)
    
    print(f"输入: {input_data2.shape} -> 输出: {output2.shape}")
    
    # 场景3: 批量处理不同尺寸（需要分别处理）
    print("\n场景3: 处理多种尺寸的数据")
    different_sizes = [
        (torch.randn(1, 32, 15, 23), (15, 23)),
        (torch.randn(1, 32, 20, 30), (20, 30)),
        (torch.randn(1, 32, 12, 18), (12, 18))
    ]
    
    for i, (data, size) in enumerate(different_sizes):
        print(f"\n  数据 {i+1}: {data.shape}")
        encoder = create_adaptive_encoder(size, channels=32, embed_dim=128)
        
        with torch.no_grad():
            output = encoder(data)
        
        print(f"  输出: {output.shape}")

def main():
    """主函数"""
    print("Heat2D_ViT 尺寸适配能力演示")
    
    # 运行所有测试
    test_different_resolutions()
    test_heat2d_direct()
    practical_usage_example()
    
    print(f"\n{'='*80}")
    print("总结")
    print(f"{'='*80}")
    print("1. Heat2D_ViT 支持任意矩形分辨率")
    print("2. 只需修改 resolution 参数即可适配不同尺寸")
    print("3. 无需重新训练，算法会动态生成对应的变换矩阵")
    print("4. 支持从极端矩形到正方形的各种尺寸")
    print("5. 使用 create_adaptive_encoder() 函数可以快速创建适配编码器")

if __name__ == "__main__":
    main()
