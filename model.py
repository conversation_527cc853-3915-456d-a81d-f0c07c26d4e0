"""
@authors: <PERSON>, et. al
@organization: GRASP Lab, University of Pennsylvania
@date: ...
@license: ...

@brief: This module contains the models that were used in the paper "Utilizing vision transformer models for end-to-end vision-based
quadrotor obstacle avoidance" by <PERSON><PERSON><PERSON><PERSON>, et. al
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import LSTM
import torch.nn.utils.spectral_norm as spectral_norm
from ViTsubmodules import *
from GoTsubmodules import GoTEncoderLayer

def refine_inputs(X):

    # fill quaternion rotation if not given
    # make it [1, 0, 0, 0] repeated with numrows = X[0].shape[0]
    if X[2] is None:
        # X[2] = torch.Tensor([1, 0, 0, 0]).float()
        X[2] = torch.zeros((X[0].shape[0], 4)).float().to(X[0].device)
        X[2][:, 0] = 1

    # if input depth images are not of right shape, resize
    if X[0].shape[-2] != 60 or X[0].shape[-1] != 90:
        X[0] = F.interpolate(X[0], size=(60, 90), mode='bilinear')

    return X

def depth_to_bins_lid(depth_map, depth_min=0.0, depth_max=1.0, num_bins=6):
    """
    将深度图转换为深度分层索引，使用基于指数函数的固定区间分层方法

    新的分层方式使用指数分布的预定义区间，根据num_bins动态生成：
    - 对于num_bins=6的情况（7个区间）：
      * 区间0: [0, e^(-6)]           ≈ [0, 0.0025]
      * 区间1: [e^(-6), e^(-5)]      ≈ [0.0025, 0.0067]
      * 区间2: [e^(-5), e^(-4)]      ≈ [0.0067, 0.0183]
      * 区间3: [e^(-4), e^(-3)]      ≈ [0.0183, 0.0498]
      * 区间4: [e^(-3), e^(-2)]      ≈ [0.0498, 0.1353]
      * 区间5: [e^(-2), e^(-1)]      ≈ [0.1353, 0.3679]
      * 区间6: [e^(-1), 1]           ≈ [0.3679, 1.0]

    Args:
        depth_map (torch.Tensor): 输入深度图，形状为 [B, H, W] 或 [H, W]
        depth_min (float): 最小深度值（保留参数以维持接口兼容性，但不影响分层）
        depth_max (float): 最大深度值（保留参数以维持接口兼容性，但不影响分层）
        num_bins (int): 分层数量，决定指数区间的数量，默认为6（生成7个区间）

    Returns:
        torch.Tensor: 深度分层索引，与输入形状相同，值范围为[0, num_bins]

    优势：
        1. 固定区间：不依赖于输入数据的最大最小值，具有更好的一致性
        2. 指数分布：更符合深度感知的非线性特性，近距离物体有更高的分辨率
        3. 数值稳定：避免了原方法中的开方运算，减少数值误差
        4. 语义明确：每个区间都有明确的物理意义
        5. 灵活性：支持不同的num_bins值，自动调整指数区间
    """
    import math

    # 根据num_bins动态生成指数区间边界
    # 使用从 e^(-(num_bins+1)) 到 e^(-1) 的指数序列，最后加上1.0
    boundaries = [0.0]  # 第一个边界始终为0

    # 生成指数边界：e^(-(num_bins+1)), e^(-num_bins), ..., e^(-2), e^(-1)
    for i in range(num_bins + 1, 0, -1):
        boundaries.append(math.exp(-i))

    # 最后一个边界为1.0
    boundaries.append(1.0)

    # 转换为张量
    boundaries = torch.tensor(boundaries, device=depth_map.device, dtype=depth_map.dtype)

    # 处理边界情况：将超出[0,1]范围的值裁剪到有效范围
    depth_map_clipped = torch.clamp(depth_map, 0.0, 1.0)

    # 使用 torch.searchsorted 进行高效的区间查找
    # searchsorted 返回每个深度值应该插入的位置，即对应的区间索引
    # 使用 boundaries[1:-1] 排除首尾边界，因为我们要找的是区间索引
    indices = torch.searchsorted(boundaries[1:-1], depth_map_clipped, right=False)

    # 确保索引在有效范围内 [0, num_bins]
    indices = torch.clamp(indices, 0, num_bins)

    # 转换为长整型，适用于后续的embedding或分类操作
    return indices.long()


def split_depth_map_to_layers(depth_map, depth_min=0.0, depth_max=1.0, num_bins=6, mask_value=-1.0):
    """
    将深度图按照LID方法分成多层，每层只包含属于该层的像素，其他位置用0填充

    Args:
        depth_map (torch.Tensor): 输入深度图，形状为 [B, H, W] 或 [H, W]
        depth_min (float): 最小深度值
        depth_max (float): 最大深度值
        num_bins (int): 分层数量
        Returns:
         torch.Tensor: 分层后的深度图，形状为 [B, num_bins, H, W] 或 [num_bins, H, W]
    """
    # 确保深度图至少是3维的 [B, H, W]
    if depth_map.dim() == 2:
         depth_map = depth_map.unsqueeze(0) # 添加批次维度

    B, C, H, W = depth_map.shape

    # 计算每个像素属于哪一层
    bin_indices = depth_to_bins_lid(depth_map, depth_min, depth_max, num_bins)
    bin_indices = bin_indices.long() # 转换为整数索引

    # 创建输出张量，形状为 [B, num_bins, H, W]
    # layered_depth_maps = torch.zeros((B, num_bins, H, W), device=depth_map.device)
    layered_depth_maps = torch.full((B, num_bins, H, W), mask_value, device=depth_map.device)

    # 为每一层创建掩码并填充深度值
    for b in range(B):
         for n in range(num_bins):
            # 创建当前层的掩码
            mask = (bin_indices[b] == n)

            # 将原始深度值填充到对应层的对应位置
            layered_depth_maps[b, n] = torch.where(mask, depth_map[b], torch.tensor(mask_value, device=depth_map.device))

    # 如果输入是2D的，则移除批次维度
    if depth_map.shape[0] == 1 and len(depth_map.shape) == 3:
        layered_depth_maps = layered_depth_maps.squeeze(0)

    return layered_depth_maps



class ConvNet(nn.Module):
    """
    Conv + FC Network
    Num Params: 235,269
    """
    def __init__(self):
        super().__init__()
        self.conv1 = nn.Conv2d(1, 4, 3, 3)
        self.conv2 = nn.Conv2d(4, 10, 3, 2)
        self.avgpool = nn.AvgPool2d(kernel_size=3, stride=1)
        self.maxpool = nn.MaxPool2d(2, 1)
        self.bn1 = nn.BatchNorm2d(4)

        self.fc0 = nn.Linear(845, 256, bias=False)
        self.fc1 = nn.Linear(256, 64, bias=False)
        self.fc2 = nn.Linear(64, 32, bias=False)
        self.fc3 = nn.Linear(32, 3)

    def forward(self, X):

        X = refine_inputs(X)

        x = X[0]
        # x = split_depth_map_to_layers(x)
        x = -self.maxpool(- self.bn1(F.relu(self.conv1(x))))
        x = self.avgpool(F.relu(self.conv2(x)))

        x = torch.flatten(x, 1)  # flatten all dimensions except batch

        metadata = torch.cat((X[1]*0.1, X[2]), dim=1).float()

        x = torch.cat((x, metadata), dim=1).float()

        x = F.leaky_relu(self.fc0(x))
        x = F.leaky_relu(self.fc1(x))
        x = torch.tanh(self.fc2(x))
        x = self.fc3(x)

        return x, None

class EncoderQ(nn.Module):
    def __init__(self, input_size, output_size):
        super(EncoderQ, self).__init__()
        self.fc = nn.Linear(input_size, output_size)

    def forward(self, x):
        return self.fc(x)


class Decoder(nn.Module):
    def __init__(self, input_size, output_size):
        super(Decoder, self).__init__()
        self.fc = nn.Linear(input_size, output_size)

    def forward(self, x):
        return self.fc(x)

class LSTMNet(nn.Module):
    """
    LSTM + FC Network
    Num Params: 2,949,937
    """
    def __init__(self):
        super().__init__()
        # 卷积层1：输入通道数为1，输出通道数为4，卷积核大小为5x5，步幅为3，填充为1
        self.conv1 = nn.Conv2d(1, 4, 5, stride=3, padding=1)
        # 卷积层2：输入通道数为4，输出通道数为10，卷积核大小为3x3，步幅为2，填充为0
        self.conv2 = nn.Conv2d(4, 10, 3, stride=2, padding=0)
        # 平均池化层：核大小为3x3，步幅为1
        self.avgpool = nn.AvgPool2d(kernel_size=3, stride=1)
        # 最大池化层：核大小为3x3，步幅为1
        self.maxpool = nn.MaxPool2d(3, 1)
        # 批归一化层：对4个通道进行归一化
        self.bn1 = nn.BatchNorm2d(4)
        # 批归一化层：对10个通道进行归一化
        self.bn2 = nn.BatchNorm2d(10)

        # LSTM层：输入特征数调整为包含 encoderQ 输出、速度、四元组和隐状态的维度
        self.lstm_input_size = 395
        self.lstm_hidden_size = 395
        self.lstm = LSTM(input_size=self.lstm_input_size, hidden_size=self.lstm_hidden_size,
                         num_layers=2, dropout=0.15, bias=False)
        # encoderQ
        self.encoderQ = EncoderQ(660 + 395, 128)

        self.fcQ1 = spectral_norm(nn.Linear(128 + 1 + 4, 395))

        # decoder
        self.decoder = Decoder(395, 128)


        # 全连接层：输入特征数为395，输出特征数为64
        self.fc1 = spectral_norm(nn.Linear(395 + 128, 64))
        # 全连接层：输入特征数为64，输出特征数为16
        self.fc2 = spectral_norm(nn.Linear(64, 16))
        # 全连接层：输入特征数为16，输出特征数为3（最终输出）
        self.fc3 = spectral_norm(nn.Linear(16, 3))

    def forward(self, X):
        # 预处理输入数据，确保深度图大小为60x90，若四元数为 None 则进行填充
        X = refine_inputs(X)
        batch_size = X[0].shape[0]

        # 提取深度图像，形状为 (B, 1, 60, 90)，B 为批次大小
        x = X[0]

        # 输出形状变为 (B, 4, 20, 29)
        x = F.relu(self.conv1(x))
        # 经过批归一化层 1，形状不变，仍为 (B, 4, 20, 29)
        x = self.bn1(x)
        # 输出形状变为 (B, 4, 18, 27)
        x = -self.maxpool(-x)

        # 输出形状变为 (B, 10, 8, 13)
        x = F.relu(self.conv2(x))
        # 经过批归一化层 2，形状不变，仍为 (B, 10, 8, 13)
        x = self.bn2(x)

        # 输出形状变为 (B, 10, 6, 11)
        x = self.avgpool(x)

        # 将特征图展平为一维向量，形状变为 (B, 10 * 6 * 11 = 660)
        x = torch.flatten(x, 1)

        if len(X) > 4:
            x = self.encoderQ(torch.cat(x, X[4]), dim=1).float() # 输入为 (B, 660 + 395)，输出为 (B, 128)
        else:
            l = torch.zeros(batch_size, self.lstm_input_size).to(X[0].device)
            x = self.encoderQ(torch.cat((x, l), dim=1), dim=1).float() #输入为 (B, 660 + 395)，输出为 (B, 128)


        # 连接后的形状变为 (B, 128 + 1 + 4 = 133)
        x = torch.cat((x, X[1] * 0.1, X[2]), dim=1).float()

        x = self.fcQ1(x) # 输入为 (B, 133)，输出为 (B, 395)

        # 如果提供了隐藏状态，则传入 LSTM
        if len(X) > 3:
            # 输入形状为 (B, 395)，输出形状为 (B, 395)
            l, h = self.lstm(x, X[3])
        else:
            # 输入形状为 (B, 395)，输出形状为 (B, 395)
            l, h = self.lstm(x)

        d = self.decoder(l) # 输入为 (B, 395)，输出为 (B, 128)
        x = torch.cat((d, l), dim=1).float()#  连接后的形状变为 (B, 128 + 395)
        # 经过全连接层 1，输入特征数 395 + 128，输出特征数 64
        # 形状变为 (B, 64)
        x = F.leaky_relu(self.fc1(x))

        # 经过全连接层 2，输入特征数 64，输出特征数 16
        # 形状变为 (B, 16)
        x = F.leaky_relu(self.fc2(x))

        # 经过全连接层 3，输入特征数 16，输出特征数 3
        # 形状变为 (B, 3)
        x = self.fc3(x)

        return x, h, l,

class LSTMNetVIT(nn.Module):
    """
    ViT+LSTM Network
    结合了 Vision Transformer 的特征提取能力和 LSTM 的时序建模能力
    功能：
        1. 使用 MixTransformerEncoderLayer 提取深度图像特征
        2. 通过解码器将特征映射到较低维度
        3. 结合期望速度和四元数信息
        4. 使用 LSTM 进行时序建模
        5. 通过全连接层生成控制命令
    输入：
        X: 列表形式的输入数据，包含深度图像、期望速度、四元数和可选的隐藏状态
        其中深度图像形状为 (B, 1, 60, 90)，B 为批次大小
    输出：
        控制命令(3维向量)和 LSTM 隐藏状态
    Num Params: 3,563,663
    """
    def __init__(self):
        super().__init__()
        self.SEAttention = SEModule(6)
        self.encoder_blocks = nn.ModuleList([
            MixTransformerEncoderLayer(6, 32, patch_size=7, stride=4, padding=3, n_layers=2, reduction_ratio=8, num_heads=1, expansion_factor=8),
            MixTransformerEncoderLayer(32, 64, patch_size=3, stride=2, padding=1, n_layers=2, reduction_ratio=4, num_heads=2, expansion_factor=8)
        ])

        self.decoder = spectral_norm(nn.Linear(4608, 512))
        self.lstm = (nn.LSTM(input_size=517, hidden_size=128,
                         num_layers=3, dropout=0.1))
        self.nn_fc2 = spectral_norm(nn.Linear(128, 3))

        self.up_sample = nn.Upsample(size=(16,24), mode='bilinear', align_corners=True)
        self.pxShuffle = nn.PixelShuffle(upscale_factor=2)
        self.down_sample = nn.Conv2d(48,12,3, padding = 1)

    def forward(self, X):

        X = refine_inputs(X)

        x = X[0]
        x = split_depth_map_to_layers(x)
        x = self.SEAttention(x)
        embeds = [x]
        for block in self.encoder_blocks:
            embeds.append(block(embeds[-1]))
        out = embeds[1:]
        out = torch.cat([self.pxShuffle(out[1]),self.up_sample(out[0])],dim=1)
        out = self.down_sample(out)
        out = self.decoder(out.flatten(1))
        out = torch.cat([out, X[1]/10, X[2]], dim=1).float()
        if len(X)>3:
            out,h = self.lstm(out, X[3])
        else:
            out,h = self.lstm(out)
        out = self.nn_fc2(out)
        return out, h

class ViT(nn.Module):
    """
    ViT+FC Network
    Num Params: 3,101,199
    """
    def __init__(self):
        super().__init__()
        self.SEAttention = SEModule(6)
        # self.fc1 = nn.Linear(5400, 512)
        # self.fc2 = nn.Linear(512, 5400)
        # self.lstm = (nn.LSTM(input_size=512, hidden_size=128,
        #                  num_layers=3, dropout=0.1))
        self.encoder_blocks = nn.ModuleList([
            HeatMixTransformerEncoderLayer(6, 32, patch_size=7, stride=4, padding=3, n_layers=2, reduction_ratio=8, num_heads=1, expansion_factor=8),
            HeatMixTransformerEncoderLayer(32, 64, patch_size=3, stride=2, padding=1, n_layers=2, reduction_ratio=4, num_heads=2, expansion_factor=8)
        ])
        self.decoder = nn.Linear(4608, 512)
        self.nn_fc1 = spectral_norm(nn.Linear(517, 256))
        self.nn_fc2 = spectral_norm(nn.Linear(256, 3))
        self.up_sample = nn.Upsample(size=(16,24), mode='bilinear', align_corners=True)
        self.pxShuffle = nn.PixelShuffle(upscale_factor=2)
        self.down_sample = nn.Conv2d(48,12,3, padding = 1)

    def forward(self, X):

        X = refine_inputs(X)

        x = X[0]
        x = split_depth_map_to_layers(x)
        # B, C, H, W = x.shape
        x = self.SEAttention(x)
        # x = x.reshape(B,C,-1) # 形状为[B, C, H*W]
        # x = self.fc1(x)
        # x = self.lstm(x)
        # x = self.fc2(x)
        # x = x.reshape(B,C,H,W)
        embeds = [x]
        for block in self.encoder_blocks:
            embeds.append(block(embeds[-1]))
        out = embeds[1:]
        out = torch.cat([self.pxShuffle(out[1]),self.up_sample(out[0])],dim=1)
        out = self.down_sample(out)
        out = self.decoder(out.flatten(1))
        out = torch.cat([out, X[1]/10, X[2]], dim=1).float()
        out = F.leaky_relu(self.nn_fc1(out))
        out = self.nn_fc2(out)

        return out, None

class UNetConvLSTMNet(nn.Module):
    """
    UNet+LSTM Network
    Num Params: 2,955,822
    """

    def __init__(self):
        super().__init__()

        self.unet_e11 = nn.Conv2d(1, 4, kernel_size=3, padding=1)
        self.unet_e12 = nn.Conv2d(4, 4, kernel_size=3, padding=1) #(N, 4, 60, 90)
        self.unet_pool1 = nn.MaxPool2d(kernel_size=2, stride=3,) #(N, 4, 30, 45)

        self.unet_e21 = nn.Conv2d(4, 8, kernel_size=3, padding=1) #(N, 8, 26, 41)
        self.unet_e22 = nn.Conv2d(8, 8, kernel_size=3, padding=1) #(N, 8, 24, 39)
        self.unet_pool2 = nn.MaxPool2d(kernel_size=2, stride=2,) #(N, 8, 12, 19)

        #Input: (N, 8, 12, 19)
        self.unet_e31 = nn.Conv2d(8, 16, kernel_size=3, padding=1) #(N, 8, 10, 17)
        self.unet_e32 = nn.Conv2d(16, 16, kernel_size=3, padding=1) #(N, 16, 8, 15)

        self.unet_upconv1 = nn.ConvTranspose2d(16, 8, kernel_size=2, stride=2,)
        self.unet_d11 = nn.Conv2d(16, 8, kernel_size=3, padding=1)
        self.unet_d12 = nn.Conv2d(8, 8, kernel_size=3, padding=1)

        self.unet_upconv2 = nn.ConvTranspose2d(8, 4, kernel_size=3, stride=3,)
        self.unet_d21 = nn.Conv2d(8, 4, kernel_size=3, padding=1)
        self.unet_d22 = nn.Conv2d(4, 4, kernel_size=3, padding=1)

        self.unet_out = nn.Conv2d(4, 1, kernel_size=1)

        self.conv_conv1 = nn.Conv2d(2, 4, 5, 3)
        self.conv_conv2 = nn.Conv2d(4, 10, 5, 2)
        self.conv_avgpool = nn.AvgPool2d(kernel_size=2, stride=1)
        self.conv_maxpool = nn.MaxPool2d(2, 1)
        self.conv_bn1 = nn.BatchNorm2d(4)

        self.lstm = LSTM(input_size=3065, hidden_size=200, num_layers=2, dropout=0.15, bias=False)

        self.nn_fc1 = torch.nn.utils.spectral_norm(nn.Linear(200, 64))
        self.nn_fc2 = torch.nn.utils.spectral_norm(nn.Linear(64, 32))
        self.nn_fc3 = torch.nn.utils.spectral_norm(nn.Linear(32, 3))

    def forward(self, X):

        X = refine_inputs(X)

        img, des_vel, quat = X[0], X[1], X[2]
        y_e1 = torch.relu(self.unet_e12(torch.relu(self.unet_e11(img))))
        unet_enc1 = self.unet_pool1(y_e1)
        y_e2 = torch.relu(self.unet_e22(torch.relu(self.unet_e21(unet_enc1))))
        unet_enc2 = self.unet_pool2(y_e2)
        y_e3 = torch.relu(self.unet_e32(torch.relu(self.unet_e31(unet_enc2))))

        unet_dec1 = torch.relu(self.unet_d12(torch.relu(self.unet_d11(torch.cat([self.unet_upconv1(y_e3), y_e2], dim=1)))))
        unet_dec2 = torch.relu(self.unet_d22(torch.relu(self.unet_d21(torch.cat([self.unet_upconv2(unet_dec1), y_e1], dim=1)))))

        y_unet = self.unet_out(unet_dec2)
        x_conv = torch.cat((img, y_unet), dim=1)

        y_conv = -self.conv_maxpool(-torch.relu(self.conv_bn1(self.conv_conv1(x_conv))))
        y_conv = self.conv_avgpool(torch.relu(self.conv_conv2(y_conv)))

        x_lstm = torch.cat([torch.flatten(y_conv, 1), torch.flatten(y_e3, 1), des_vel*0.1, quat], dim=1).float()

        if len(X)>3:
            y_lstm, h = self.lstm(x_lstm, X[3])
        else:
            y_lstm, h = self.lstm(x_lstm)


        y_fc1 = F.leaky_relu(self.nn_fc1(y_lstm))
        y_fc2 = F.leaky_relu(self.nn_fc2(y_fc1))
        y = self.nn_fc3(y_fc2)

        return y, h

class CustomNet(nn.Module):
    """
    自定义网络模型，结合CNN和Transformer特点
    """
    def __init__(self):
        super().__init__()
        # CNN部分用于特征提取
        self.conv1 = nn.Conv2d(1, 16, kernel_size=5, stride=2, padding=2)
        self.bn1 = nn.BatchNorm2d(16)
        self.conv2 = nn.Conv2d(16, 32, kernel_size=3, stride=2, padding=1)
        self.bn2 = nn.BatchNorm2d(32)

        # 使用一个简化版的Transformer编码器
        self.encoder_block = MixTransformerEncoderLayer(
            32, 64, patch_size=3, stride=1, padding=1,
            n_layers=2, reduction_ratio=4, num_heads=2, expansion_factor=4
        )

        # 解码器部分
        self.decoder = nn.Linear(4608, 256)
        self.fc1 = spectral_norm(nn.Linear(261, 128))  # 256 + 1 (速度) + 4 (四元数)
        self.fc2 = spectral_norm(nn.Linear(128, 64))
        self.fc3 = spectral_norm(nn.Linear(64, 3))

        # LSTM部分用于时序建模
        self.lstm = nn.LSTM(input_size=261, hidden_size=128, num_layers=2, dropout=0.1)

    def forward(self, X):
        X = refine_inputs(X)

        x = X[0]  # 图像输入

        # CNN特征提取
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))

        # Transformer编码
        x = self.encoder_block(x)

        # 展平特征
        x = self.decoder(x.flatten(1))

        # 连接元数据（速度和四元数）
        x = torch.cat([x, X[1]/10, X[2]], dim=1).float()

        # 使用LSTM进行时序建模（如果提供了隐藏状态）
        if len(X) > 3:
            x, h = self.lstm(x, X[3])
        else:
            x, h = self.lstm(x)

        # 全连接层进行最终预测
        x = F.leaky_relu(self.fc1(x))
        x = F.leaky_relu(self.fc2(x))
        x = self.fc3(x)

        return x, h

class WindowsViT(nn.Module):
    """
    WindowsViT模型：结合窗口注意力机制和Transformer的视觉模型
    功能：
        1. 使用MixWindowsTransformerEncoderLayer提取图像特征
        2. 通过解码器和全连接层生成控制命令
    输入：
        X: 列表形式的输入数据，包含深度图像、期望速度和四元数
        其中深度图像形状为 (B, 1, 60, 90)，B 为批次大小
    输出：
        控制命令(3维向量)和None(兼容其他模型接口)
    """

    def __init__(self):
        super().__init__()
        # Vision Transformer编码器块
        # 输入图像经过两层编码器，每层包含多个窗口注意力模块
        self.encoder_blocks = nn.ModuleList([
            MixWindowsTransformerEncoderLayer(1, 32, patch_size=7, stride=3, padding=3,
                                      n_layers=2, reduction_ratio=7, num_heads=[1, 4], expansion_factor=8, window_size=[2, 10]),
            MixWindowsTransformerEncoderLayer(32, 64, patch_size=3, stride=1, padding=1,
                                      n_layers=2, reduction_ratio=3, num_heads=[2, 8], expansion_factor=8, window_size=[2, 10])
        ])
        # 解码器：将编码器输出的特征映射到较低维度
        self.decoder = nn.Linear(1200, 512)  # 输入维度为1200，输出维度为512
        # 全连接层：进一步处理特征并生成控制命令
        self.nn_fc1 = spectral_norm(nn.Linear(517, 256))  # 输入维度为517(512+5+4)，输出维度为256
        self.nn_fc2 = spectral_norm(nn.Linear(256, 3))  # 输入维度为256，输出维度为3(控制命令)

        # 特征处理层：用于调整特征图的空间尺寸
        self.up_sample = nn.Upsample(size=(16, 24), mode='bilinear', align_corners=True)
        self.pxShuffle = nn.PixelShuffle(upscale_factor=2)
        self.down_sample = nn.Conv2d(48, 12, 3, padding=1)

    def forward(self, X):
        """
        前向传播函数
        参数：
            X: 输入数据列表 [深度图像, 期望速度, 四元数]
        返回：
            控制命令(3维向量)和None(兼容其他模型接口)
        """
        # 预处理输入数据
        X = refine_inputs(X)

        # 提取深度图像，初始形状为 (B, 1, 60, 90)，B 为批次大小
        x = X[0]

        # 通过Vision Transformer编码器块
        # x = split_depth_map_to_layers(x)
        embeds = [x]
        for block in self.encoder_blocks:
            embeds.append(block(embeds[-1]))

        # 处理编码器输出(跳过原始输入)
        out = embeds[1:]
        out[0] = out[0].mean(dim=1, keepdim=True)  # 第一个编码器输出在通道维度取平均，形状从 (B, 32, 20, 30) 变为 (B, 1, 20, 30)
        out[1] = out[1].mean(dim=1, keepdim=True)  # 第二个编码器输出在通道维度取平均，形状从 (B, 64, 20, 30) 变为 (B, 1, 20, 30)
        out = torch.cat(out, dim=1)  # 拼接两个编码器输出，形状变为 (B, 2, 20, 30)

        # 将特征展平并通过解码器
        out = self.decoder(out.flatten(1))  # 展平后形状变为 (B, 2 * 20 * 30 = 1200)，经过解码器后变为 (B, 512)

        # 连接特征和元数据(期望速度和四元数)
        # 期望速度形状为 (B, 5)，四元数形状为 (B, 4)
        out = torch.cat([out, X[1] / 10, X[2]], dim=1).float()  # 输入维度变为 (B, 517)（512+5+4）

        # 通过全连接层和激活函数
        out = F.leaky_relu(self.nn_fc1(out))  # 输入维度为 (B, 517)，输出维度为 (B, 256)
        out = self.nn_fc2(out)  # 输入维度为 (B, 256)，输出维度为 (B, 3)（控制命令）

        # 返回控制命令和None(兼容其他模型接口)
        return out, None

class LSTMWindowsViT(nn.Module):
    """
    WindowsViT+LSTM Network
    结合了WindowsViT的窗口注意力机制和LSTM的时序建模能力
    """
    def __init__(self):
        super().__init__()
        # Vision Transformer编码器块，与WindowsViT中的相同
        self.encoder_blocks = nn.ModuleList([
            MixWindowsTransformerEncoderLayer(1, 32, patch_size=7, stride=3, padding=3,
                                      n_layers=2, reduction_ratio=7, num_heads=[1, 4], expansion_factor=8, window_size=[2, 10]),
            MixWindowsTransformerEncoderLayer(32, 64, patch_size=3, stride=1, padding=1,
                                      n_layers=2, reduction_ratio=3, num_heads=[2, 8], expansion_factor=8, window_size=[2, 10])
        ])
        # 解码器
        self.decoder = nn.Linear(1200, 512)  # 将ViT特征映射到较低维度，注意这里从1200改为800

        # LSTM层，用于时序建模
        self.lstm = nn.LSTM(input_size=517, hidden_size=128,
                         num_layers=3, dropout=0.1)

        # 最终输出层
        self.nn_fc2 = spectral_norm(nn.Linear(128, 3))  # 输出3维控制命令

        # 特征处理层
        # self.up_sample = nn.Upsample(size=(16,24), mode='bilinear', align_corners=True)
        # self.pxShuffle = nn.PixelShuffle(upscale_factor=2)
        # self.down_sample = nn.Conv2d(48,12,3, padding=1)



    def forward(self, X):
        """
        前向传播函数

        参数:
            X: 输入数据列表 [深度图像, 期望速度, 四元数, 可选的隐藏状态]

        返回:
            控制命令(3维向量)和LSTM隐藏状态
        """
        # 预处理输入
        X = refine_inputs(X)

        # 提取深度图像
        x = X[0]


        # 通过Vision Transformer编码器块
        embeds = [x]
        for block in self.encoder_blocks:
            embeds.append(block(embeds[-1]))

        # 处理编码器输出(跳过原始输入)
        out = embeds[1:]
        del embeds

        out[0] = out[0].mean(dim=1, keepdim=True)
        out[1] = out[1].mean(dim=1, keepdim=True)
        out = torch.cat(out, dim=1)

        # 将特征展平并通过解码器
        out = self.decoder(out.flatten(1))

        # 连接特征和元数据(期望速度和四元数)
        out = torch.cat([out, X[1]/10, X[2]], dim=1).float()

        # 通过LSTM层进行时序建模
        if len(X) > 3:
            out, h = self.lstm(out, X[3])
        else:
            out, h = self.lstm(out)

        # 最终输出层
        out = self.nn_fc2(out)

        # 返回控制命令和LSTM隐藏状态
        return out, h



class LSTMWindowsGoT(nn.Module):
    """
    WindowsViT+LSTM+GoT Network
    结合了WindowsViT的窗口注意力机制、LSTM的时序建模能力和GoT的图像-四元数融合能力

    特点：
    1. 第一层使用MixWindowsTransformerEncoderLayer处理图像
    2. 第二层使用GoTTransformerEncoderLayer处理图像和四元数
    3. GoT层将图像切成6个10x10尺寸的小窗口进行处理
    """
    def __init__(self):
        super().__init__()
        # 第一个Vision Transformer编码器块，与LSTMWindowsViT中的相同


        self.encoder_block1 = MixWindowsTransformerEncoderLayer(1, 32, patch_size=7, stride=3, padding=3,
                                  n_layers=2, reduction_ratio=7, num_heads=[1, 2], expansion_factor=8, window_size=[2, 10])

        # 第二个编码器块使用GoT Transformer，可以处理图像和四元数
        self.encoder_block2 = GoTEncoderLayer(32, 32, patch_size=3, stride=1, padding=1,
                                n_layers=2, num_heads=8, dim=64, window_size=10)

        # 解码器
        self.fc1 = nn.Linear(64, 600)
        self.decoder = nn.Linear(1200, 512)  # 将特征映射到较低维度

        # LSTM层，用于时序建模
        self.lstm = nn.LSTM(input_size=513, hidden_size=128,
                         num_layers=3, dropout=0.1)

        # 最终输出层
        self.nn_fc2 = spectral_norm(nn.Linear(128, 3))  # 输出3维控制命令

    def forward(self, X):
        """
        前向传播函数

        参数:
            X: 输入数据列表 [深度图像, 期望速度, 四元数, 可选的隐藏状态]

        返回:
            控制命令(3维向量)和LSTM隐藏状态
        """
        # 预处理输入
        X = refine_inputs(X)
        # 提取深度图像和四元数
        x = X[0]
        quat = X[2]
        # 通过第一个Vision Transformer编码器块
        x1 = self.encoder_block1(x)# [b, c, h, w]
        # 通过GoT Transformer编码器块，同时处理图像特征和四元数
        x2 = self.encoder_block2(x1, quat) # [b, n, dim]
        x1 = x1.mean(dim=1).flatten(1)#[b,dim]
        x2 = x2[:, 0] # [b, dim]
        x2 = self.fc1(x2)
        out = torch.cat([x1, x2], dim=1)
        # 将特征展平并通过解码器
        out = self.decoder(out)
        # 连接特征和元数据(期望速度和四元数)
        out = torch.cat([out, X[1]/10], dim=1).float()
        # 通过LSTM层进行时序建模
        if len(X) > 3:
            out, h = self.lstm(out, X[3])
        else:
            out, h = self.lstm(out)

        # 最终输出层
        out = self.nn_fc2(out)

        # 返回控制命令和LSTM隐藏状态
        return out, h


if __name__ == '__main__':
    print("MODEL NUM PARAMS ARE")
    model = ConvNet().float()
    print("ConvNet: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = LSTMNet().float()
    print("LSTMNet: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = UNetConvLSTMNet().float()
    print("UNET: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = ViT().float()
    print("VIT: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = LSTMNetVIT().float()
    print("VITLSTM: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = CustomNet().float()
    print("CustomNet: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = WindowsViT().float()
    print("WindowsViT: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = LSTMWindowsViT().float()
    print("LSTMWindowsViT: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

    model = LSTMWindowsGoT().float()
    print("LSTMWindowsGoT: ")
    print(sum(p.numel() for p in model.parameters() if p.requires_grad))

