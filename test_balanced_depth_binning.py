#!/usr/bin/env python3
"""
测试平衡的深度分层策略
验证调整后的 t^1.5 幂函数分层效果
"""

import torch
import math

def depth_to_bins_lid_balanced(depth_map, depth_min=0.0, depth_max=1.0, num_bins=6):
    """
    平衡的深度分层函数，使用 t^1.5 幂函数
    """
    split_points = []
    for i in range(1, num_bins + 1):
        t = i / (num_bins + 1)
        # 使用 t^1.5 幂函数
        boundary = t ** 1.5
        split_points.append(boundary)
    
    boundaries = [0.0] + split_points + [1.0]
    boundaries = torch.tensor(boundaries, device=depth_map.device, dtype=depth_map.dtype)
    
    depth_map_clipped = torch.clamp(depth_map, 0.0, 1.0)
    indices = torch.searchsorted(boundaries[1:-1], depth_map_clipped, right=False)
    indices = torch.clamp(indices, 0, num_bins)
    
    return indices.long()

def depth_to_bins_lid_old(depth_map, depth_min=0.0, depth_max=1.0, num_bins=6):
    """
    原来过度精细化的版本，使用 1-(1-t)^2
    """
    split_points = []
    for i in range(1, num_bins + 1):
        t = i / (num_bins + 1)
        # 使用反向二次函数
        boundary = 1.0 - (1.0 - t) ** 2
        split_points.append(boundary)
    
    boundaries = [0.0] + split_points + [1.0]
    boundaries = torch.tensor(boundaries, device=depth_map.device, dtype=depth_map.dtype)
    
    depth_map_clipped = torch.clamp(depth_map, 0.0, 1.0)
    indices = torch.searchsorted(boundaries[1:-1], depth_map_clipped, right=False)
    indices = torch.clamp(indices, 0, num_bins)
    
    return indices.long()

def compare_strategies():
    """对比新旧分层策略"""
    print("=" * 80)
    print("新旧分层策略对比")
    print("=" * 80)
    
    num_bins = 6
    
    # 计算新策略的边界
    print("新策略 (t^1.5) - 平衡分层:")
    new_boundaries = []
    for i in range(1, num_bins + 1):
        t = i / (num_bins + 1)
        boundary = t ** 1.5
        new_boundaries.append(boundary)
    new_boundaries = [0.0] + new_boundaries + [1.0]
    
    # 计算旧策略的边界
    print("\n旧策略 (1-(1-t)^2) - 过度精细化:")
    old_boundaries = []
    for i in range(1, num_bins + 1):
        t = i / (num_bins + 1)
        boundary = 1.0 - (1.0 - t) ** 2
        old_boundaries.append(boundary)
    old_boundaries = [0.0] + old_boundaries + [1.0]
    
    # 对比分析
    print("\n详细对比:")
    print("区间 | 新策略区间范围        | 新策略大小 | 旧策略区间范围        | 旧策略大小 | 改进")
    print("-" * 95)
    
    for i in range(len(new_boundaries) - 1):
        new_size = new_boundaries[i+1] - new_boundaries[i]
        old_size = old_boundaries[i+1] - old_boundaries[i]
        
        if new_size > old_size:
            improvement = f"增大 {new_size/old_size:.1f}x"
        else:
            improvement = f"减小 {old_size/new_size:.1f}x"
        
        print(f"{i:2d}   | [{new_boundaries[i]:.3f}, {new_boundaries[i+1]:.3f}] | {new_size:.3f}     | "
              f"[{old_boundaries[i]:.3f}, {old_boundaries[i+1]:.3f}] | {old_size:.3f}     | {improvement}")
    
    # 分析最关键的远距离区间
    print(f"\n关键改进分析:")
    print(f"远距离区间 (最后两个区间) 的改进:")
    
    # 倒数第二个区间
    new_size_2nd = new_boundaries[-2] - new_boundaries[-3]
    old_size_2nd = old_boundaries[-2] - old_boundaries[-3]
    print(f"倒数第2个区间: 新策略 {new_size_2nd:.3f} vs 旧策略 {old_size_2nd:.3f} (增大 {new_size_2nd/old_size_2nd:.1f}x)")
    
    # 最后一个区间
    new_size_last = new_boundaries[-1] - new_boundaries[-2]
    old_size_last = old_boundaries[-1] - old_boundaries[-2]
    print(f"最后区间:    新策略 {new_size_last:.3f} vs 旧策略 {old_size_last:.3f} (增大 {new_size_last/old_size_last:.1f}x)")

def test_depth_mapping_comparison():
    """测试深度值映射的对比"""
    print(f"\n{'='*80}")
    print("深度值映射对比")
    print(f"{'='*80}")
    
    # 重点测试远距离区间
    test_depths = torch.tensor([0.0, 0.2, 0.4, 0.6, 0.8, 0.85, 0.9, 0.92, 0.94, 0.96, 0.98, 0.99, 1.0])
    
    new_indices = depth_to_bins_lid_balanced(test_depths, num_bins=6)
    old_indices = depth_to_bins_lid_old(test_depths, num_bins=6)
    
    print("深度值 | 新策略索引 | 旧策略索引 | 差异说明")
    print("-" * 55)
    
    for depth, new_idx, old_idx in zip(test_depths, new_indices, old_indices):
        if new_idx == old_idx:
            diff = "相同"
        else:
            diff = f"新策略区间更大" if new_idx < old_idx else "新策略区间更小"
        
        print(f"{depth:.2f}   |     {new_idx.item()}      |     {old_idx.item()}      | {diff}")

def analyze_data_distribution():
    """分析数据分布特性"""
    print(f"\n{'='*80}")
    print("数据分布分析")
    print(f"{'='*80}")
    
    # 创建均匀分布的深度值
    uniform_depths = torch.linspace(0, 1, 10000)
    
    new_indices = depth_to_bins_lid_balanced(uniform_depths, num_bins=6)
    old_indices = depth_to_bins_lid_old(uniform_depths, num_bins=6)
    
    # 统计每个区间的数据量
    new_unique, new_counts = torch.unique(new_indices, return_counts=True)
    old_unique, old_counts = torch.unique(old_indices, return_counts=True)
    
    print("均匀深度分布下的数据分布对比:")
    print("区间 | 新策略数据量 | 新策略占比 | 旧策略数据量 | 旧策略占比 | 数据稀疏性改进")
    print("-" * 80)
    
    total = len(uniform_depths)
    for i in range(7):  # 7个区间
        new_count = new_counts[i].item() if i < len(new_counts) else 0
        old_count = old_counts[i].item() if i < len(old_counts) else 0
        
        new_pct = new_count / total * 100
        old_pct = old_count / total * 100
        
        if old_count > 0:
            improvement = f"{new_count/old_count:.1f}x数据量"
        else:
            improvement = "N/A"
        
        print(f"{i:2d}   | {new_count:8d}     | {new_pct:6.1f}%   | {old_count:8d}     | {old_pct:6.1f}%   | {improvement}")
    
    # 分析数据稀疏性
    print(f"\n数据稀疏性分析:")
    min_new = min(new_counts).item()
    min_old = min(old_counts).item()
    print(f"最少数据区间: 新策略 {min_new} vs 旧策略 {min_old}")
    print(f"稀疏性改进: {min_new/min_old:.1f}x")

def test_practical_scenarios():
    """测试实际应用场景"""
    print(f"\n{'='*80}")
    print("实际应用场景测试")
    print(f"{'='*80}")
    
    # 场景1: 室内深度图（大部分物体在中近距离）
    print("场景1: 室内深度图模拟")
    indoor_depths = torch.cat([
        torch.rand(500) * 0.3,      # 50% 近距离物体 [0, 0.3]
        torch.rand(300) * 0.4 + 0.3, # 30% 中距离物体 [0.3, 0.7]
        torch.rand(200) * 0.3 + 0.7  # 20% 远距离物体 [0.7, 1.0]
    ])
    
    new_indoor = depth_to_bins_lid_balanced(indoor_depths, num_bins=6)
    old_indoor = depth_to_bins_lid_old(indoor_depths, num_bins=6)
    
    print("室内场景区间分布:")
    new_unique, new_counts = torch.unique(new_indoor, return_counts=True)
    old_unique, old_counts = torch.unique(old_indoor, return_counts=True)
    
    for i in range(7):
        new_count = new_counts[i].item() if i < len(new_counts) else 0
        old_count = old_counts[i].item() if i < len(old_counts) else 0
        print(f"区间{i}: 新策略 {new_count:3d} vs 旧策略 {old_count:3d}")
    
    # 场景2: 户外深度图（更多远距离物体）
    print(f"\n场景2: 户外深度图模拟")
    outdoor_depths = torch.cat([
        torch.rand(200) * 0.3,      # 20% 近距离物体
        torch.rand(300) * 0.4 + 0.3, # 30% 中距离物体
        torch.rand(500) * 0.3 + 0.7  # 50% 远距离物体
    ])
    
    new_outdoor = depth_to_bins_lid_balanced(outdoor_depths, num_bins=6)
    old_outdoor = depth_to_bins_lid_old(outdoor_depths, num_bins=6)
    
    print("户外场景区间分布:")
    new_unique, new_counts = torch.unique(new_outdoor, return_counts=True)
    old_unique, old_counts = torch.unique(old_outdoor, return_counts=True)
    
    for i in range(7):
        new_count = new_counts[i].item() if i < len(new_counts) else 0
        old_count = old_counts[i].item() if i < len(old_counts) else 0
        print(f"区间{i}: 新策略 {new_count:3d} vs 旧策略 {old_count:3d}")

def main():
    """主测试函数"""
    print("开始测试平衡的深度分层策略")
    
    compare_strategies()
    test_depth_mapping_comparison()
    analyze_data_distribution()
    test_practical_scenarios()
    
    print(f"\n{'='*80}")
    print("总结")
    print(f"{'='*80}")
    
    print("新策略 (t^1.5) 的优势:")
    print("✓ 避免过度精细化：远距离区间大小更合理")
    print("✓ 减少数据稀疏性：每个区间都有足够的数据")
    print("✓ 保持平衡性：仍然实现远距离相对精细，近距离相对粗糙")
    print("✓ 数值稳定性：简单的幂函数，计算高效")
    print("✓ 实用性更强：适合实际深度图像处理需求")
    
    print(f"\n关键改进:")
    print("- 最后两个区间的大小显著增加，减少了数据稀疏问题")
    print("- 整体分布更加平衡，避免了极端的精细化")
    print("- 在保持深度感知优势的同时，提高了实用性")

if __name__ == "__main__":
    main()
